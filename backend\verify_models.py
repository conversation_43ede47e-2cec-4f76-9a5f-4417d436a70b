#!/usr/bin/env python3
"""
Script to verify and download all required model components
"""
import os
import sys
from pathlib import Path

# Set up local model storage before importing anything else
MODELS_DIR = Path(__file__).parent / "models"
MODELS_DIR.mkdir(exist_ok=True)

os.environ["HF_HOME"] = str(MODELS_DIR / "huggingface")
os.environ["TRANSFORMERS_CACHE"] = str(MODELS_DIR / "transformers")
os.environ["DIFFUSERS_CACHE"] = str(MODELS_DIR / "diffusers")

# Create subdirectories
for cache_dir in ["huggingface", "transformers", "diffusers"]:
    (MODELS_DIR / cache_dir).mkdir(exist_ok=True)

def verify_and_download_models():
    """Verify and download all required models"""
    
    print(f"🔍 Verifying models in: {MODELS_DIR}")
    print(f"📁 Total current size: {get_directory_size(MODELS_DIR):.2f} GB\n")
    
    models_to_verify = [
        {
            "name": "Stable Video Diffusion (Main)",
            "model_id": "stabilityai/stable-video-diffusion-img2vid-xt",
            "type": "video"
        },
        {
            "name": "Stable Diffusion (Image)",
            "model_id": "runwayml/stable-diffusion-v1-5", 
            "type": "image"
        }
    ]
    
    for model_info in models_to_verify:
        print(f"🔄 Checking: {model_info['name']}")
        print(f"   Model ID: {model_info['model_id']}")
        
        try:
            if model_info['type'] == 'video':
                verify_video_model(model_info['model_id'])
            else:
                verify_image_model(model_info['model_id'])
            print(f"   ✅ Model verified and ready\n")
            
        except Exception as e:
            print(f"   ❌ Error: {e}\n")
    
    print(f"📁 Final total size: {get_directory_size(MODELS_DIR):.2f} GB")
    print("🎉 Model verification complete!")

def verify_video_model(model_id: str):
    """Verify video generation model"""
    try:
        from diffusers import StableVideoDiffusionPipeline
        import torch
        
        print(f"   📥 Loading/verifying video model...")
        
        # This will download missing components if needed
        pipe = StableVideoDiffusionPipeline.from_pretrained(
            model_id,
            torch_dtype=torch.float16,
            variant="fp16",
            cache_dir=str(MODELS_DIR / "diffusers"),
            local_files_only=False,
            resume_download=True,
            use_safetensors=True
        )
        
        print(f"   ✅ Video model components verified")
        
        # Clean up memory
        del pipe
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
    except ImportError as e:
        print(f"   ⚠️  Import error: {e}")
        print(f"   💡 Run: pip install diffusers torch torchvision")
        raise
    except Exception as e:
        print(f"   ❌ Model verification failed: {e}")
        raise

def verify_image_model(model_id: str):
    """Verify image generation model"""
    try:
        from diffusers import StableDiffusionPipeline
        import torch
        
        print(f"   📥 Loading/verifying image model...")
        
        # This will download missing components if needed
        pipe = StableDiffusionPipeline.from_pretrained(
            model_id,
            torch_dtype=torch.float16,
            cache_dir=str(MODELS_DIR / "diffusers"),
            local_files_only=False,
            resume_download=True,
            use_safetensors=True
        )
        
        print(f"   ✅ Image model components verified")
        
        # Clean up memory
        del pipe
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
    except Exception as e:
        print(f"   ❌ Model verification failed: {e}")
        raise

def get_directory_size(path: Path) -> float:
    """Get directory size in GB"""
    total_size = 0
    try:
        for item in path.rglob('*'):
            if item.is_file():
                total_size += item.stat().st_size
    except Exception:
        pass
    return total_size / (1024**3)

def check_gpu_availability():
    """Check GPU availability"""
    try:
        import torch
        print(f"🖥️  CUDA Available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"🎮 GPU Device: {torch.cuda.get_device_name(0)}")
            print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        else:
            print("⚠️  Using CPU (slower generation)")
        print()
    except ImportError:
        print("❌ PyTorch not installed")

if __name__ == "__main__":
    print("🚀 Model Verification and Download Script")
    print("=" * 50)
    
    check_gpu_availability()
    verify_and_download_models()
