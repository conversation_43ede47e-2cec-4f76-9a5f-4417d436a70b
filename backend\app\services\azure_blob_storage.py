import logging
from typing import Op<PERSON>, <PERSON><PERSON>, BinaryIO
import uuid
from pathlib import Path
import mimetypes
from urllib.parse import urljoin

from azure.storage.blob import BlobServiceClient, BlobClient
from azure.core.exceptions import AzureError

from app.core.config import settings

logger = logging.getLogger(__name__)


class AzureBlobStorageService:
    """Service for Azure Blob Storage operations"""
    
    def __init__(self):
        self.sas_url = settings.BLOB_SAS_URL
        self.sas_token = settings.BLOB_SAS_TOKEN
        self.container_name = settings.PDF_BUCKET_NAME
        
        if not self.sas_url or not self.sas_token or not self.container_name:
            logger.warning("Azure Blob Storage credentials not fully configured")
            self.blob_service_client = None
        else:
            try:
                # Create blob service client with SAS URL
                account_url = self.sas_url.split('/')[0] + '//' + self.sas_url.split('/')[2]
                self.blob_service_client = BlobServiceClient(
                    account_url=account_url,
                    credential=self.sas_token
                )
                logger.info("Azure Blob Storage client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Azure Blob Storage client: {e}")
                self.blob_service_client = None
    
    def is_configured(self) -> bool:
        """Check if Azure Blob Storage is properly configured"""
        return self.blob_service_client is not None
    
    async def upload_file(
        self,
        file_content: bytes,
        filename: str,
        folder: str = "",
        content_type: Optional[str] = None
    ) -> Tuple[str, str]:
        """
        Upload file to Azure Blob Storage
        
        Args:
            file_content: File content as bytes
            filename: Name of the file
            folder: Optional folder/prefix for the file
            content_type: MIME type of the file
        
        Returns:
            Tuple of (blob_name, public_url)
        """
        if not self.is_configured():
            raise ValueError("Azure Blob Storage is not configured")
        
        try:
            # Generate unique blob name
            file_extension = Path(filename).suffix
            unique_filename = f"{uuid.uuid4().hex[:8]}_{filename}"
            
            if folder:
                blob_name = f"{folder}/{unique_filename}"
            else:
                blob_name = unique_filename
            
            # Determine content type
            if not content_type:
                content_type, _ = mimetypes.guess_type(filename)
                if not content_type:
                    content_type = "application/octet-stream"
            
            # Get blob client
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=blob_name
            )
            
            # Upload file
            blob_client.upload_blob(
                file_content,
                content_type=content_type,
                overwrite=True
            )
            
            # Generate public URL
            public_url = f"{self.sas_url}/{blob_name}"
            
            logger.info(f"File uploaded successfully: {blob_name}")
            
            return blob_name, public_url
            
        except AzureError as e:
            logger.error(f"Azure Blob Storage upload error: {e}")
            raise Exception(f"Failed to upload file to Azure Blob Storage: {e}")
        except Exception as e:
            logger.error(f"Unexpected error during file upload: {e}")
            raise
    
    async def upload_image(self, file_content: bytes, filename: str) -> Tuple[str, str]:
        """Upload image file to Azure Blob Storage"""
        return await self.upload_file(
            file_content=file_content,
            filename=filename,
            folder="images",
            content_type="image/png"
        )
    
    async def upload_video(self, file_content: bytes, filename: str) -> Tuple[str, str]:
        """Upload video file to Azure Blob Storage"""
        return await self.upload_file(
            file_content=file_content,
            filename=filename,
            folder="videos",
            content_type="video/mp4"
        )
    
    async def upload_temp_file(self, file_content: bytes, filename: str) -> Tuple[str, str]:
        """Upload temporary file to Azure Blob Storage"""
        return await self.upload_file(
            file_content=file_content,
            filename=filename,
            folder="temp"
        )
    
    async def delete_file(self, blob_name: str) -> bool:
        """
        Delete file from Azure Blob Storage
        
        Args:
            blob_name: Name of the blob to delete
        
        Returns:
            True if successful, False otherwise
        """
        if not self.is_configured():
            logger.warning("Azure Blob Storage is not configured")
            return False
        
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=blob_name
            )
            
            blob_client.delete_blob()
            logger.info(f"File deleted successfully: {blob_name}")
            return True
            
        except AzureError as e:
            logger.error(f"Azure Blob Storage delete error: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during file deletion: {e}")
            return False
    
    async def get_file_url(self, blob_name: str) -> Optional[str]:
        """
        Get public URL for a blob
        
        Args:
            blob_name: Name of the blob
        
        Returns:
            Public URL or None if not found
        """
        if not self.is_configured():
            return None
        
        try:
            return f"{self.sas_url}/{blob_name}"
        except Exception as e:
            logger.error(f"Error generating file URL: {e}")
            return None
    
    async def file_exists(self, blob_name: str) -> bool:
        """
        Check if file exists in Azure Blob Storage
        
        Args:
            blob_name: Name of the blob to check
        
        Returns:
            True if file exists, False otherwise
        """
        if not self.is_configured():
            return False
        
        try:
            blob_client = self.blob_service_client.get_blob_client(
                container=self.container_name,
                blob=blob_name
            )
            
            return blob_client.exists()
            
        except AzureError as e:
            logger.error(f"Azure Blob Storage exists check error: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during file existence check: {e}")
            return False
    
    async def list_files(self, folder: str = "") -> list:
        """
        List files in a folder
        
        Args:
            folder: Folder prefix to filter by
        
        Returns:
            List of blob names
        """
        if not self.is_configured():
            return []
        
        try:
            container_client = self.blob_service_client.get_container_client(
                self.container_name
            )
            
            blobs = []
            blob_list = container_client.list_blobs(name_starts_with=folder)
            
            for blob in blob_list:
                blobs.append(blob.name)
            
            return blobs
            
        except AzureError as e:
            logger.error(f"Azure Blob Storage list error: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error during file listing: {e}")
            return []


# Global instance
azure_blob_storage = AzureBlobStorageService()
