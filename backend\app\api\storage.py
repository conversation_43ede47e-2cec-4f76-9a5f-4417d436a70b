from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any
import logging

from app.core.security import get_current_user_token
from app.services.azure_blob_storage import azure_blob_storage
from app.utils.file_manager import file_manager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/storage", tags=["Storage"])


class StorageStatusResponse(BaseModel):
    azure_blob_configured: bool
    s3_configured: bool
    local_storage_path: str
    primary_storage: str
    azure_blob_details: Dict[str, Any]


@router.get("/status", response_model=StorageStatusResponse)
async def get_storage_status():
    """Get storage configuration status"""
    
    storage_info = file_manager.get_storage_info()
    
    azure_blob_details = {
        "configured": azure_blob_storage.is_configured(),
        "container_name": azure_blob_storage.container_name if azure_blob_storage.is_configured() else None,
        "sas_url": azure_blob_storage.sas_url if azure_blob_storage.is_configured() else None
    }
    
    return StorageStatusResponse(
        azure_blob_configured=storage_info["azure_blob_configured"],
        s3_configured=storage_info["s3_configured"],
        local_storage_path=storage_info["local_storage_path"],
        primary_storage=storage_info["primary_storage"],
        azure_blob_details=azure_blob_details
    )


@router.post("/test-upload")
async def test_upload(
    current_user: dict = Depends(get_current_user_token)
):
    """Test file upload to configured storage"""
    
    try:
        # Create a test file
        test_content = b"This is a test file for storage verification."
        test_filename = "test_upload.txt"
        
        # Save using file manager (will use Azure Blob Storage if configured)
        file_path, file_url = await file_manager.save_file(
            file_content=test_content,
            filename=test_filename,
            subfolder="test"
        )
        
        logger.info(f"Test file uploaded successfully by user {current_user['user_id']}")
        
        return {
            "message": "Test upload successful",
            "file_path": file_path,
            "file_url": file_url,
            "storage_used": file_manager.get_storage_info()["primary_storage"]
        }
        
    except Exception as e:
        logger.error(f"Test upload failed for user {current_user['user_id']}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Test upload failed: {str(e)}"
        )


@router.get("/azure-blob/list")
async def list_azure_blob_files(
    folder: str = "",
    current_user: dict = Depends(get_current_user_token)
):
    """List files in Azure Blob Storage"""
    
    if not azure_blob_storage.is_configured():
        raise HTTPException(
            status_code=503,
            detail="Azure Blob Storage is not configured"
        )
    
    try:
        files = await azure_blob_storage.list_files(folder)
        
        return {
            "folder": folder,
            "files": files,
            "count": len(files)
        }
        
    except Exception as e:
        logger.error(f"Failed to list Azure Blob files: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list files: {str(e)}"
        )
