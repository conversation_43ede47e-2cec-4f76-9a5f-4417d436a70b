# Development Guide

This guide will help you set up and develop the Video Generation Application.

## 🚀 Quick Start

### Prerequisites

- **Docker & Docker Compose**: For running databases and services
- **Python 3.11+**: For the backend API
- **Node.js 18+**: For the frontend application
- **Git**: For version control

### Automated Setup

Run the setup script to automatically configure everything:

```bash
./setup.sh
```

This script will:
- Check all prerequisites
- Set up environment files
- Install backend and frontend dependencies
- Create necessary directories
- Set up the database

### Manual Setup

If you prefer to set up manually:

1. **<PERSON><PERSON> and navigate to the project**:
   ```bash
   git clone <repository-url>
   cd video-gen-app
   ```

2. **Set up environment files**:
   ```bash
   cp .env.example .env
   cp frontend/.env.example frontend/.env
   ```

3. **Install backend dependencies**:
   ```bash
   cd backend
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   cd ..
   ```

4. **Install frontend dependencies**:
   ```bash
   cd frontend
   npm install
   cd ..
   ```

5. **Start database services**:
   ```bash
   docker-compose up -d postgres redis
   ```

6. **Run database migrations**:
   ```bash
   cd backend
   source venv/bin/activate
   alembic upgrade head
   cd ..
   ```

## 🔧 Configuration

### Environment Variables

Edit the `.env` file with your configuration:

```env
# Database
DATABASE_URL=postgresql://videogen_user:videogen_password@localhost:5432/videogen_db
REDIS_URL=redis://localhost:6379/0

# JWT
SECRET_KEY=your-super-secret-jwt-key-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# AI APIs (Optional)
OPENAI_API_KEY=your-openai-api-key
RUNWAYML_API_KEY=your-runwayml-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key
STABILITY_API_KEY=your-stability-ai-api-key

# Cloud Storage (Choose one)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=your-s3-bucket-name
```

### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/auth/callback` (development)
   - Your production domain callback URL
6. Copy Client ID and Secret to your `.env` file

## 🏃‍♂️ Running the Application

### Development Mode

1. **Start all services**:
   ```bash
   docker-compose up -d
   ```

2. **Start backend server**:
   ```bash
   cd backend
   source venv/bin/activate
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Start frontend server** (in another terminal):
   ```bash
   cd frontend
   npm run dev
   ```

4. **Start Celery worker** (in another terminal):
   ```bash
   cd backend
   source venv/bin/activate
   celery -A app.celery_app worker --loglevel=info
   ```

### Access Points

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Flower (Celery monitoring)**: http://localhost:5555
- **Database**: localhost:5432 (videogen_db)
- **Redis**: localhost:6379

## 🧪 Testing

### Backend Tests

```bash
cd backend
source venv/bin/activate
pytest
```

### Frontend Tests

```bash
cd frontend
npm test
```

## 📦 Building for Production

### Backend

```bash
cd backend
docker build -t videogen-backend .
```

### Frontend

```bash
cd frontend
npm run build
docker build -t videogen-frontend .
```

### Full Stack

```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🔍 Debugging

### Backend Debugging

1. **Check logs**:
   ```bash
   docker-compose logs backend
   ```

2. **Database connection**:
   ```bash
   docker-compose exec postgres psql -U videogen_user -d videogen_db
   ```

3. **Redis connection**:
   ```bash
   docker-compose exec redis redis-cli
   ```

### Frontend Debugging

1. **Check browser console** for JavaScript errors
2. **Network tab** to inspect API calls
3. **React Developer Tools** for component debugging

## 📁 Project Structure

```
video-gen-app/
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── alembic/            # Database migrations
│   ├── tests/              # Backend tests
│   └── requirements.txt    # Python dependencies
├── frontend/               # React.js frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── package.json        # Node.js dependencies
├── docker-compose.yml      # Development environment
├── .env.example           # Environment variables template
└── setup.sh              # Automated setup script
```

## 🚀 Deployment

### Using Docker

1. **Build images**:
   ```bash
   docker-compose build
   ```

2. **Deploy to production**:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Manual Deployment

1. **Backend**: Deploy to any Python hosting service (AWS, Heroku, DigitalOcean)
2. **Frontend**: Deploy to static hosting (Vercel, Netlify, AWS S3)
3. **Database**: Use managed PostgreSQL (AWS RDS, Google Cloud SQL)
4. **Redis**: Use managed Redis (AWS ElastiCache, Redis Cloud)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📚 Additional Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://react.dev/)
- [Docker Documentation](https://docs.docker.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Celery Documentation](https://docs.celeryproject.org/)

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use**: Change ports in docker-compose.yml
2. **Database connection failed**: Check PostgreSQL is running
3. **API calls failing**: Verify backend is running and CORS is configured
4. **Video generation not working**: Check Celery worker is running
5. **Google OAuth not working**: Verify client ID and redirect URIs

### Getting Help

- Check the logs first: `docker-compose logs [service-name]`
- Ensure all services are running: `docker-compose ps`
- Verify environment variables are set correctly
- Check API documentation at http://localhost:8000/docs
