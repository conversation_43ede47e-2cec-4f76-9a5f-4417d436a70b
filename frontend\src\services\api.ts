import axios, { AxiosInstance, AxiosResponse } from 'axios';
import toast from 'react-hot-toast';
import { 
  User, 
  Video, 
  Prompt, 
  VideoGenerationRequest, 
  LoginResponse, 
  UserStats,
  UploadLimits,
  UploadResponse,
  PaginatedResponse,
  ApiError
} from '@/types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              localStorage.setItem('access_token', response.access_token);
              originalRequest.headers.Authorization = `Bearer ${response.access_token}`;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        // Handle other errors
        const apiError: ApiError = {
          message: error.response?.data?.detail || error.message || 'An error occurred',
          status: error.response?.status || 500,
          code: error.response?.data?.code,
          details: error.response?.data
        };

        // Show toast for errors (except 401 which is handled above)
        if (error.response?.status !== 401) {
          toast.error(apiError.message);
        }

        return Promise.reject(apiError);
      }
    );
  }

  // Auth endpoints
  async googleAuth(token: string): Promise<LoginResponse> {
    const response = await this.api.post('/auth/google', { token });
    return response.data;
  }

  async refreshToken(refreshToken: string): Promise<{ access_token: string }> {
    const response = await this.api.post('/auth/refresh', { refresh_token: refreshToken });
    return response.data;
  }

  async logout(): Promise<void> {
    await this.api.post('/auth/logout');
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get('/auth/me');
    return response.data;
  }

  // User endpoints
  async getUserProfile(): Promise<User> {
    const response = await this.api.get('/users/profile');
    return response.data;
  }

  async updateUserProfile(data: Partial<User>): Promise<User> {
    const response = await this.api.put('/users/profile', data);
    return response.data;
  }

  async getUserStats(): Promise<UserStats> {
    const response = await this.api.get('/users/stats');
    return response.data;
  }

  async deleteAccount(): Promise<void> {
    await this.api.delete('/users/account');
  }

  async upgradeToPremium(): Promise<{ message: string; expires_at: string }> {
    const response = await this.api.post('/users/upgrade-premium');
    return response.data;
  }

  // Video endpoints
  async generateVideo(data: VideoGenerationRequest): Promise<Video> {
    const response = await this.api.post('/videos/generate', data);
    return response.data;
  }

  async getVideos(page = 1, perPage = 20, statusFilter?: string): Promise<PaginatedResponse<Video>> {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString(),
    });
    
    if (statusFilter) {
      params.append('status_filter', statusFilter);
    }

    const response = await this.api.get(`/videos?${params}`);
    return {
      items: response.data.videos,
      total: response.data.total,
      page: response.data.page,
      per_page: response.data.per_page,
      total_pages: Math.ceil(response.data.total / response.data.per_page)
    };
  }

  async getVideo(id: number): Promise<Video> {
    const response = await this.api.get(`/videos/${id}`);
    return response.data;
  }

  async deleteVideo(id: number): Promise<void> {
    await this.api.delete(`/videos/${id}`);
  }

  async downloadVideo(id: number): Promise<{ download_url: string; filename: string; file_size_mb: number }> {
    const response = await this.api.post(`/videos/${id}/download`);
    return response.data;
  }

  // Prompt endpoints
  async createPrompt(data: Partial<Prompt>): Promise<Prompt> {
    const response = await this.api.post('/prompts', data);
    return response.data;
  }

  async getPrompts(page = 1, perPage = 20, category?: string, style?: string, publicOnly = false): Promise<PaginatedResponse<Prompt>> {
    const params = new URLSearchParams({
      page: page.toString(),
      per_page: perPage.toString(),
      public_only: publicOnly.toString(),
    });
    
    if (category) params.append('category', category);
    if (style) params.append('style', style);

    const response = await this.api.get(`/prompts?${params}`);
    return {
      items: response.data.prompts,
      total: response.data.total,
      page: response.data.page,
      per_page: response.data.per_page,
      total_pages: Math.ceil(response.data.total / response.data.per_page)
    };
  }

  async getPopularPrompts(limit = 10): Promise<Prompt[]> {
    const response = await this.api.get(`/prompts/popular?limit=${limit}`);
    return response.data;
  }

  async getPromptCategories(): Promise<{ categories: string[]; styles: string[]; moods: string[] }> {
    const response = await this.api.get('/prompts/categories');
    return response.data;
  }

  async getPrompt(id: number): Promise<Prompt> {
    const response = await this.api.get(`/prompts/${id}`);
    return response.data;
  }

  async updatePrompt(id: number, data: Partial<Prompt>): Promise<Prompt> {
    const response = await this.api.put(`/prompts/${id}`, data);
    return response.data;
  }

  async deletePrompt(id: number): Promise<void> {
    await this.api.delete(`/prompts/${id}`);
  }

  // Upload endpoints
  async uploadImage(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async uploadVideo(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.api.post('/upload/video', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getUploadLimits(): Promise<UploadLimits> {
    const response = await this.api.get('/upload/limits');
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
