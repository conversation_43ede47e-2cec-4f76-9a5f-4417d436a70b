import os
import uuid
import hashlib
import mimetypes
from pathlib import Path
from typing import Op<PERSON>, <PERSON><PERSON>, BinaryIO
import logging
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

from app.core.config import settings

logger = logging.getLogger(__name__)


class FileManager:
    """Handles file operations including local storage and cloud storage"""
    
    def __init__(self):
        self.local_storage_path = settings.upload_path
        self.use_cloud_storage = bool(settings.AWS_ACCESS_KEY_ID or settings.CLOUDFLARE_R2_ACCESS_KEY_ID)
        
        # Initialize cloud storage client
        if settings.AWS_ACCESS_KEY_ID:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION
            )
            self.bucket_name = settings.AWS_BUCKET_NAME
        elif settings.CLOUDFLARE_R2_ACCESS_KEY_ID:
            self.s3_client = boto3.client(
                's3',
                endpoint_url=settings.CLOUDFLARE_R2_ENDPOINT,
                aws_access_key_id=settings.CLOUDFLARE_R2_ACCESS_KEY_ID,
                aws_secret_access_key=settings.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
                region_name='auto'
            )
            self.bucket_name = settings.CLOUDFLARE_R2_BUCKET_NAME
        else:
            self.s3_client = None
            self.bucket_name = None
    
    def generate_unique_filename(self, original_filename: str) -> str:
        """Generate a unique filename while preserving the extension"""
        file_extension = Path(original_filename).suffix.lower()
        unique_id = str(uuid.uuid4())
        return f"{unique_id}{file_extension}"
    
    def get_file_hash(self, file_content: bytes) -> str:
        """Generate SHA-256 hash of file content"""
        return hashlib.sha256(file_content).hexdigest()
    
    def validate_file_type(self, filename: str, allowed_extensions: list) -> bool:
        """Validate file type based on extension"""
        file_extension = Path(filename).suffix.lower().lstrip('.')
        return file_extension in [ext.lower() for ext in allowed_extensions]
    
    def validate_file_size(self, file_size: int, max_size_mb: int) -> bool:
        """Validate file size"""
        max_size_bytes = max_size_mb * 1024 * 1024
        return file_size <= max_size_bytes
    
    def get_mime_type(self, filename: str) -> str:
        """Get MIME type of file"""
        mime_type, _ = mimetypes.guess_type(filename)
        return mime_type or 'application/octet-stream'
    
    async def save_file(
        self, 
        file_content: bytes, 
        filename: str, 
        subfolder: str = "uploads"
    ) -> Tuple[str, str]:
        """
        Save file to storage (local or cloud)
        Returns: (file_path, file_url)
        """
        unique_filename = self.generate_unique_filename(filename)
        
        if self.use_cloud_storage and self.s3_client:
            return await self._save_to_cloud(file_content, unique_filename, subfolder)
        else:
            return await self._save_to_local(file_content, unique_filename, subfolder)
    
    async def _save_to_local(
        self, 
        file_content: bytes, 
        filename: str, 
        subfolder: str
    ) -> Tuple[str, str]:
        """Save file to local storage"""
        try:
            # Create directory if it doesn't exist
            local_dir = self.local_storage_path / subfolder
            local_dir.mkdir(parents=True, exist_ok=True)
            
            # Save file
            file_path = local_dir / filename
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            # Generate URL
            file_url = f"{settings.BACKEND_URL}/uploads/{subfolder}/{filename}"
            
            logger.info(f"File saved locally: {file_path}")
            return str(file_path), file_url
            
        except Exception as e:
            logger.error(f"Error saving file locally: {e}")
            raise
    
    async def _save_to_cloud(
        self, 
        file_content: bytes, 
        filename: str, 
        subfolder: str
    ) -> Tuple[str, str]:
        """Save file to cloud storage (S3 or R2)"""
        try:
            # S3 key
            s3_key = f"{subfolder}/{filename}"
            
            # Upload to S3/R2
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=file_content,
                ContentType=self.get_mime_type(filename),
                ServerSideEncryption='AES256'
            )
            
            # Generate URL
            if settings.AWS_ACCESS_KEY_ID:
                file_url = f"https://{self.bucket_name}.s3.{settings.AWS_REGION}.amazonaws.com/{s3_key}"
            else:
                # CloudFlare R2
                file_url = f"{settings.CLOUDFLARE_R2_ENDPOINT}/{self.bucket_name}/{s3_key}"
            
            logger.info(f"File saved to cloud: {s3_key}")
            return s3_key, file_url
            
        except ClientError as e:
            logger.error(f"Error saving file to cloud: {e}")
            raise
        except NoCredentialsError:
            logger.error("Cloud storage credentials not found")
            raise
    
    async def delete_file(self, file_path: str) -> bool:
        """Delete file from storage"""
        try:
            if self.use_cloud_storage and self.s3_client:
                return await self._delete_from_cloud(file_path)
            else:
                return await self._delete_from_local(file_path)
        except Exception as e:
            logger.error(f"Error deleting file: {e}")
            return False
    
    async def _delete_from_local(self, file_path: str) -> bool:
        """Delete file from local storage"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"File deleted locally: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting local file: {e}")
            return False
    
    async def _delete_from_cloud(self, s3_key: str) -> bool:
        """Delete file from cloud storage"""
        try:
            self.s3_client.delete_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            logger.info(f"File deleted from cloud: {s3_key}")
            return True
        except ClientError as e:
            logger.error(f"Error deleting cloud file: {e}")
            return False
    
    async def get_file_info(self, file_path: str) -> Optional[dict]:
        """Get file information"""
        try:
            if self.use_cloud_storage and self.s3_client:
                return await self._get_cloud_file_info(file_path)
            else:
                return await self._get_local_file_info(file_path)
        except Exception as e:
            logger.error(f"Error getting file info: {e}")
            return None
    
    async def _get_local_file_info(self, file_path: str) -> Optional[dict]:
        """Get local file information"""
        try:
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'exists': True
            }
        except Exception as e:
            logger.error(f"Error getting local file info: {e}")
            return None
    
    async def _get_cloud_file_info(self, s3_key: str) -> Optional[dict]:
        """Get cloud file information"""
        try:
            response = self.s3_client.head_object(
                Bucket=self.bucket_name,
                Key=s3_key
            )
            return {
                'size': response['ContentLength'],
                'modified': response['LastModified'].timestamp(),
                'exists': True,
                'etag': response['ETag']
            }
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return {'exists': False}
            logger.error(f"Error getting cloud file info: {e}")
            return None
    
    def get_storage_stats(self) -> dict:
        """Get storage statistics"""
        return {
            'storage_type': 'cloud' if self.use_cloud_storage else 'local',
            'local_path': str(self.local_storage_path),
            'cloud_bucket': self.bucket_name if self.use_cloud_storage else None,
            'cloud_provider': 'aws' if settings.AWS_ACCESS_KEY_ID else 'cloudflare' if settings.CLOUDFLARE_R2_ACCESS_KEY_ID else None
        }


# Global file manager instance
file_manager = FileManager()
