-- Initialize database with extensions and default data

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON videos(user_id);
CREATE INDEX IF NOT EXISTS idx_videos_status ON videos(status);
CREATE INDEX IF NOT EXISTS idx_videos_created_at ON videos(created_at);
CREATE INDEX IF NOT EXISTS idx_prompts_user_id ON prompts(user_id);
CREATE INDEX IF NOT EXISTS idx_prompts_public ON prompts(is_public);
CREATE INDEX IF NOT EXISTS idx_prompts_category ON prompts(category);

-- Insert default prompt templates
INSERT INTO prompts (user_id, title, prompt_text, category, style, mood, is_public, is_template, created_at) VALUES
(1, 'Cinematic Landscape', 'A breathtaking cinematic landscape with rolling hills, golden hour lighting, and dramatic clouds in the sky', 'nature', 'cinematic', 'peaceful', true, true, NOW()),
(1, 'Abstract Art Motion', 'Abstract colorful shapes flowing and morphing in smooth motion, vibrant colors, fluid dynamics', 'abstract', 'artistic', 'energetic', true, true, NOW()),
(1, 'Urban City Life', 'Bustling city street with people walking, cars moving, neon lights reflecting on wet pavement', 'urban', 'realistic', 'dynamic', true, true, NOW()),
(1, 'Ocean Waves', 'Peaceful ocean waves gently crashing on a sandy beach, seagulls flying overhead, sunset colors', 'nature', 'realistic', 'peaceful', true, true, NOW()),
(1, 'Space Journey', 'Journey through space with stars, nebulae, and distant galaxies, cosmic colors and ethereal movement', 'space', 'cinematic', 'mysterious', true, true, NOW())
ON CONFLICT DO NOTHING;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON videos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_prompts_updated_at BEFORE UPDATE ON prompts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
