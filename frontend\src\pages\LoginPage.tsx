import React, { useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Video, Shield, Zap, Users } from 'lucide-react';
import { GoogleAuthResponse } from '@/types';

const LoginPage: React.FC = () => {
  const { login } = useAuth();
  const googleButtonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initialize Google Sign-In button
    if (window.google && googleButtonRef.current) {
      window.google.accounts.id.initialize({
        client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
        callback: handleGoogleResponse,
        auto_select: false,
        cancel_on_tap_outside: true,
      });

      window.google.accounts.id.renderButton(
        googleButtonRef.current,
        {
          theme: 'outline',
          size: 'large',
          width: '100%',
          text: 'signin_with',
          shape: 'rectangular',
        }
      );
    }
  }, []);

  const handleGoogleResponse = async (response: GoogleAuthResponse) => {
    try {
      await login(response);
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const features = [
    {
      icon: Video,
      title: 'AI Video Generation',
      description: 'Create stunning videos from text prompts using advanced AI models'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Generate professional videos in minutes, not hours'
    },
    {
      icon: Shield,
      title: 'Secure & Private',
      description: 'Your content is protected with enterprise-grade security'
    },
    {
      icon: Users,
      title: 'Collaborative',
      description: 'Share and collaborate with your team seamlessly'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex">
      {/* Left side - Login form */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <div>
            <Link to="/" className="flex items-center space-x-2 mb-8">
              <Video className="h-8 w-8 text-primary-600" />
              <span className="text-2xl font-bold text-gray-900">VideoGen Studio</span>
            </Link>
            <h2 className="text-3xl font-bold text-gray-900">
              Welcome back
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Sign in to your account to start creating amazing videos
            </p>
          </div>

          <div className="mt-8">
            <div>
              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-white text-gray-500">Sign in with</span>
                  </div>
                </div>

                <div className="mt-6">
                  <div 
                    ref={googleButtonRef}
                    className="w-full flex justify-center"
                  />
                </div>
              </div>
            </div>

            <div className="mt-8">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Shield className="h-5 w-5 text-blue-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      Secure Authentication
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>
                        We use Google OAuth for secure authentication. Your credentials are never stored on our servers.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <p className="text-center text-sm text-gray-600">
                Don't have an account?{' '}
                <span className="font-medium text-primary-600">
                  Sign up automatically with Google
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Features showcase */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-600 to-primary-800">
          <div className="flex flex-col justify-center h-full px-12">
            <div className="max-w-md">
              <h2 className="text-3xl font-bold text-white mb-6">
                Create Videos Like Never Before
              </h2>
              <p className="text-primary-100 mb-8">
                Join thousands of creators who are using AI to bring their ideas to life. 
                Generate professional-quality videos in minutes.
              </p>
              
              <div className="space-y-6">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-8 w-8 rounded-md bg-primary-500">
                        <feature.icon className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-white">
                        {feature.title}
                      </h3>
                      <p className="text-sm text-primary-100">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-8 pt-8 border-t border-primary-500">
                <div className="flex items-center space-x-4 text-primary-100">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">10K+</div>
                    <div className="text-sm">Videos Created</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">5K+</div>
                    <div className="text-sm">Happy Users</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">99.9%</div>
                    <div className="text-sm">Uptime</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
