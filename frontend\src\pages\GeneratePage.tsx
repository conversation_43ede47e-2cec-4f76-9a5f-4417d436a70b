import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import toast from 'react-hot-toast';
import { apiService } from '@/services/api';
import { VideoModel, VideoGenerationForm } from '@/types';
import LoadingSpinner from '@/components/LoadingSpinner';
import {
  Sparkles,
  Upload,
  Settings,
  Play,
  Image as ImageIcon,
  Wand2,
  Info,
  X
} from 'lucide-react';

const videoGenerationSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  prompt: z.string().min(10, 'Prompt must be at least 10 characters').max(1000, 'Prompt must be less than 1000 characters'),
  negativePrompt: z.string().max(500, 'Negative prompt must be less than 500 characters').optional(),
  model: z.nativeEnum(VideoModel),
  resolution: z.string(),
  duration: z.number().min(1).max(30),
  guidanceScale: z.number().min(1).max(20),
  steps: z.number().min(10).max(50),
  seed: z.number().optional(),
});

type VideoGenerationFormData = z.infer<typeof videoGenerationSchema>;

const GeneratePage: React.FC = () => {
  const navigate = useNavigate();
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm<VideoGenerationFormData>({
    resolver: zodResolver(videoGenerationSchema),
    defaultValues: {
      model: VideoModel.STABLE_VIDEO_DIFFUSION,
      resolution: '1024x576',
      duration: 4,
      guidanceScale: 7.5,
      steps: 25,
    }
  });

  // Fetch popular prompts for inspiration
  const { data: popularPrompts } = useQuery({
    queryKey: ['popular-prompts'],
    queryFn: () => apiService.getPopularPrompts(5),
  });

  // Generate video mutation
  const generateVideoMutation = useMutation({
    mutationFn: async (data: VideoGenerationFormData) => {
      let inputImageUrl = undefined;
      
      // Upload image if provided
      if (uploadedImage) {
        const uploadResponse = await apiService.uploadImage(uploadedImage);
        inputImageUrl = uploadResponse.file_url;
      }

      return apiService.generateVideo({
        title: data.title,
        prompt_text: data.prompt,
        negative_prompt: data.negativePrompt,
        input_image_url: inputImageUrl,
        model: data.model,
        resolution: data.resolution,
        duration_seconds: data.duration,
        guidance_scale: data.guidanceScale,
        num_inference_steps: data.steps,
        seed: data.seed,
      });
    },
    onSuccess: (video) => {
      toast.success('Video generation started!');
      navigate(`/videos/${video.id}`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to start video generation');
    },
  });

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setUploadedImage(null);
    setImagePreview(null);
  };

  const usePrompt = (prompt: string) => {
    setValue('prompt', prompt);
  };

  const onSubmit = (data: VideoGenerationFormData) => {
    generateVideoMutation.mutate(data);
  };

  const selectedModel = watch('model');

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Generate AI Video
        </h1>
        <p className="text-gray-600">
          Create stunning videos from text prompts and images using advanced AI models
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Settings */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Wand2 className="h-5 w-5 mr-2 text-primary-600" />
                Basic Settings
              </h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Video Title
                  </label>
                  <input
                    {...register('title')}
                    type="text"
                    className="input w-full"
                    placeholder="Enter a descriptive title for your video"
                  />
                  {errors.title && (
                    <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Prompt
                  </label>
                  <textarea
                    {...register('prompt')}
                    rows={4}
                    className="textarea w-full"
                    placeholder="Describe the video you want to create in detail..."
                  />
                  {errors.prompt && (
                    <p className="text-sm text-red-600 mt-1">{errors.prompt.message}</p>
                  )}
                  <p className="text-sm text-gray-500 mt-1">
                    Be specific and descriptive for better results
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Negative Prompt (Optional)
                  </label>
                  <textarea
                    {...register('negativePrompt')}
                    rows={2}
                    className="textarea w-full"
                    placeholder="Describe what you don't want in the video..."
                  />
                  {errors.negativePrompt && (
                    <p className="text-sm text-red-600 mt-1">{errors.negativePrompt.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    AI Model
                  </label>
                  <select {...register('model')} className="input w-full">
                    <option value={VideoModel.STABLE_VIDEO_DIFFUSION}>
                      Stable Video Diffusion (Free)
                    </option>
                    <option value={VideoModel.RUNWAYML}>
                      RunwayML (Premium)
                    </option>
                  </select>
                  <div className="mt-2 p-3 bg-blue-50 rounded-md">
                    <div className="flex">
                      <Info className="h-4 w-4 text-blue-400 mt-0.5 mr-2" />
                      <div className="text-sm text-blue-700">
                        {selectedModel === VideoModel.STABLE_VIDEO_DIFFUSION ? (
                          <p>Open-source model, great for general video generation. Free for all users.</p>
                        ) : (
                          <p>Commercial model with advanced features. Requires premium subscription.</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Image Upload */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <ImageIcon className="h-5 w-5 mr-2 text-green-600" />
                Input Image (Optional)
              </h2>
              
              {imagePreview ? (
                <div className="space-y-4">
                  <div className="relative">
                    <img
                      src={imagePreview}
                      alt="Upload preview"
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <button
                      type="button"
                      onClick={removeImage}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  <p className="text-sm text-gray-600">
                    This image will be used as the starting frame for your video
                  </p>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">
                    Upload an image to use as the starting frame
                  </p>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="btn-outline cursor-pointer"
                  >
                    Choose Image
                  </label>
                </div>
              )}
            </div>

            {/* Advanced Settings */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <button
                type="button"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center justify-between w-full text-left"
              >
                <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Settings className="h-5 w-5 mr-2 text-gray-600" />
                  Advanced Settings
                </h2>
                <span className="text-sm text-gray-500">
                  {showAdvanced ? 'Hide' : 'Show'}
                </span>
              </button>
              
              {showAdvanced && (
                <div className="mt-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Resolution
                      </label>
                      <select {...register('resolution')} className="input w-full">
                        <option value="1024x576">1024x576 (16:9)</option>
                        <option value="768x768">768x768 (1:1)</option>
                        <option value="576x1024">576x1024 (9:16)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Duration (seconds)
                      </label>
                      <input
                        {...register('duration', { valueAsNumber: true })}
                        type="number"
                        min="1"
                        max="30"
                        className="input w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Guidance Scale
                      </label>
                      <input
                        {...register('guidanceScale', { valueAsNumber: true })}
                        type="number"
                        min="1"
                        max="20"
                        step="0.5"
                        className="input w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Inference Steps
                      </label>
                      <input
                        {...register('steps', { valueAsNumber: true })}
                        type="number"
                        min="10"
                        max="50"
                        className="input w-full"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Seed (Optional)
                      </label>
                      <input
                        {...register('seed', { valueAsNumber: true })}
                        type="number"
                        className="input w-full"
                        placeholder="Leave empty for random seed"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Generate Button */}
            <button
              type="submit"
              disabled={generateVideoMutation.isPending}
              className="w-full btn-primary py-3 text-lg flex items-center justify-center space-x-2"
            >
              {generateVideoMutation.isPending ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Generating Video...</span>
                </>
              ) : (
                <>
                  <Play className="h-5 w-5" />
                  <span>Generate Video</span>
                </>
              )}
            </button>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Popular Prompts */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Sparkles className="h-5 w-5 mr-2 text-yellow-600" />
                Popular Prompts
              </h3>
              <div className="space-y-3">
                {popularPrompts?.map((prompt) => (
                  <div
                    key={prompt.id}
                    className="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors"
                    onClick={() => usePrompt(prompt.prompt_text)}
                  >
                    <h4 className="font-medium text-gray-900 text-sm mb-1">
                      {prompt.title}
                    </h4>
                    <p className="text-xs text-gray-600 line-clamp-2">
                      {prompt.prompt_text}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500">
                        Used {prompt.use_count} times
                      </span>
                      <button
                        type="button"
                        className="text-xs text-primary-600 hover:text-primary-700"
                      >
                        Use this prompt
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Tips */}
            <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                💡 Tips for Better Results
              </h3>
              <ul className="space-y-2 text-sm text-blue-800">
                <li>• Be specific and descriptive in your prompts</li>
                <li>• Include style keywords like "cinematic", "realistic", or "artistic"</li>
                <li>• Mention lighting, camera angles, and mood</li>
                <li>• Use negative prompts to avoid unwanted elements</li>
                <li>• Higher guidance scale = more prompt adherence</li>
              </ul>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default GeneratePage;
