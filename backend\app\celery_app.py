from celery import Celery
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# Create Celery instance
celery_app = Celery(
    "video_generation",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=["app.services.video_generation"]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    result_expires=3600,  # 1 hour
)

# Task routing
celery_app.conf.task_routes = {
    "app.services.video_generation.generate_video_task": {"queue": "video_generation"},
}

# Register tasks
@celery_app.task(bind=True, name="generate_video_task")
def generate_video_task_celery(self, video_id: int):
    """Celery task wrapper for video generation"""
    from app.services.video_generation import generate_video_task
    
    try:
        # Update task progress
        self.update_state(state="PROGRESS", meta={"progress": 0})
        
        # Execute the actual task
        generate_video_task(video_id)
        
        # Task completed
        return {"status": "completed", "video_id": video_id}
        
    except Exception as e:
        logger.error(f"Celery video generation task failed: {e}")
        self.update_state(
            state="FAILURE",
            meta={"error": str(e), "video_id": video_id}
        )
        raise


if __name__ == "__main__":
    celery_app.start()
