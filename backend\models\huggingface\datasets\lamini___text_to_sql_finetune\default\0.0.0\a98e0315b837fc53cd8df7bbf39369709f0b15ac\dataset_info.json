{"description": "", "citation": "", "homepage": "", "license": "", "features": {"input": {"dtype": "string", "_type": "Value"}, "output": {"dtype": "string", "_type": "Value"}}, "builder_name": "parquet", "dataset_name": "text_to_sql_finetune", "config_name": "default", "version": {"version_str": "0.0.0", "major": 0, "minor": 0, "patch": 0}, "splits": {"train": {"name": "train", "num_bytes": 34818227, "num_examples": 16428, "dataset_name": "text_to_sql_finetune"}, "test": {"name": "test", "num_bytes": 1050788, "num_examples": 1034, "dataset_name": "text_to_sql_finetune"}}, "download_checksums": {"hf://datasets/lamini/text_to_sql_finetune@a98e0315b837fc53cd8df7bbf39369709f0b15ac/data/train-00000-of-00001-8a9aa6c5fc4a1f00.parquet": {"num_bytes": 3691335, "checksum": null}, "hf://datasets/lamini/text_to_sql_finetune@a98e0315b837fc53cd8df7bbf39369709f0b15ac/data/test-00000-of-00001-5df4da5825ade20f.parquet": {"num_bytes": 117833, "checksum": null}}, "download_size": 3809168, "dataset_size": 35869015, "size_in_bytes": 39678183}