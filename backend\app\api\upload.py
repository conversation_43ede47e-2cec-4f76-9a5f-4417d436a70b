from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uuid
import magic
from pathlib import Path
import logging

from app.core.database import get_db
from app.core.security import get_current_user_token
from app.core.config import settings
from app.models.user import User

logger = logging.getLogger(__name__)
router = APIRouter()


def validate_file_type(file: UploadFile, allowed_extensions: List[str]) -> bool:
    """Validate file type based on extension and MIME type"""
    if not file.filename:
        return False
    
    # Check extension
    file_extension = file.filename.split('.')[-1].lower()
    if file_extension not in allowed_extensions:
        return False
    
    return True


def get_file_size(file: UploadFile) -> int:
    """Get file size in bytes"""
    file.file.seek(0, 2)  # Seek to end
    size = file.file.tell()
    file.file.seek(0)  # Reset to beginning
    return size


async def save_uploaded_file(file: UploadFile, subfolder: str) -> tuple[str, str]:
    """Save uploaded file and return file path and URL"""
    # Generate unique filename
    file_extension = file.filename.split('.')[-1].lower()
    unique_filename = f"{uuid.uuid4()}.{file_extension}"
    
    # Create file path
    file_dir = settings.upload_path / subfolder
    file_dir.mkdir(exist_ok=True)
    file_path = file_dir / unique_filename
    
    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Generate URL
    file_url = f"{settings.BACKEND_URL}/uploads/{subfolder}/{unique_filename}"
    
    return str(file_path), file_url


@router.post("/image")
async def upload_image(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Upload an image file"""
    try:
        # Validate file type
        if not validate_file_type(file, settings.ALLOWED_IMAGE_EXTENSIONS):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file type. Allowed: {', '.join(settings.ALLOWED_IMAGE_EXTENSIONS)}"
            )
        
        # Check file size
        file_size = get_file_size(file)
        if file_size > settings.max_file_size_bytes:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE_MB}MB"
            )
        
        # Check user storage quota
        user = db.query(User).filter(User.id == current_user["user_id"]).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check storage limit (free users: 1GB, premium: 10GB)
        storage_limit_mb = 10240 if user.is_premium else 1024
        if user.storage_used_mb + (file_size / 1024 / 1024) > storage_limit_mb:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Storage quota exceeded"
            )
        
        # Save file
        file_path, file_url = await save_uploaded_file(file, "images")
        
        # Update user storage
        user.storage_used_mb += file_size / 1024 / 1024
        db.commit()
        
        logger.info(f"Image uploaded: {file.filename} by user {user.email}")
        
        return {
            "message": "Image uploaded successfully",
            "filename": file.filename,
            "file_path": file_path,
            "file_url": file_url,
            "file_size_mb": round(file_size / 1024 / 1024, 2)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Image upload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload image"
        )


@router.post("/video")
async def upload_video(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Upload a video file"""
    try:
        # Validate file type
        if not validate_file_type(file, settings.ALLOWED_VIDEO_EXTENSIONS):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid file type. Allowed: {', '.join(settings.ALLOWED_VIDEO_EXTENSIONS)}"
            )
        
        # Check file size
        file_size = get_file_size(file)
        if file_size > settings.max_file_size_bytes:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE_MB}MB"
            )
        
        # Check user storage quota
        user = db.query(User).filter(User.id == current_user["user_id"]).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check storage limit
        storage_limit_mb = 10240 if user.is_premium else 1024
        if user.storage_used_mb + (file_size / 1024 / 1024) > storage_limit_mb:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Storage quota exceeded"
            )
        
        # Save file
        file_path, file_url = await save_uploaded_file(file, "videos")
        
        # Update user storage
        user.storage_used_mb += file_size / 1024 / 1024
        db.commit()
        
        logger.info(f"Video uploaded: {file.filename} by user {user.email}")
        
        return {
            "message": "Video uploaded successfully",
            "filename": file.filename,
            "file_path": file_path,
            "file_url": file_url,
            "file_size_mb": round(file_size / 1024 / 1024, 2)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Video upload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload video"
        )


@router.get("/limits")
async def get_upload_limits(
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Get upload limits for current user"""
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    storage_limit_mb = 10240 if user.is_premium else 1024
    
    return {
        "max_file_size_mb": settings.MAX_FILE_SIZE_MB,
        "storage_limit_mb": storage_limit_mb,
        "storage_used_mb": user.storage_used_mb,
        "storage_remaining_mb": storage_limit_mb - user.storage_used_mb,
        "allowed_image_extensions": settings.ALLOWED_IMAGE_EXTENSIONS,
        "allowed_video_extensions": settings.ALLOWED_VIDEO_EXTENSIONS
    }
