from sqlalchemy import Column, Integer, String, DateTime, <PERSON>ole<PERSON>, Text, JSON, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum
from app.core.database import Base


class VideoStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class VideoModel(str, Enum):
    STABLE_VIDEO_DIFFUSION = "stable_video_diffusion"
    RUNWAYML = "runwayml"
    OPENAI_SORA = "openai_sora"
    CUSTOM = "custom"


class Video(Base):
    __tablename__ = "videos"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # User relationship
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user = relationship("User", back_populates="videos")
    
    # Video metadata
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Generation parameters
    prompt_text = Column(Text, nullable=False)
    negative_prompt = Column(Text, nullable=True)
    
    # Input files
    input_image_path = Column(String(500), nullable=True)
    input_image_url = Column(String(500), nullable=True)
    
    # Output files
    output_video_path = Column(String(500), nullable=True)
    output_video_url = Column(String(500), nullable=True)
    thumbnail_path = Column(String(500), nullable=True)
    thumbnail_url = Column(String(500), nullable=True)
    
    # Generation settings
    model_used = Column(String(100), default=VideoModel.STABLE_VIDEO_DIFFUSION)
    resolution = Column(String(20), default="1024x576")  # width x height
    duration_seconds = Column(Float, default=4.0)
    fps = Column(Integer, default=24)
    
    # AI model parameters
    guidance_scale = Column(Float, default=7.5)
    num_inference_steps = Column(Integer, default=25)
    seed = Column(Integer, nullable=True)
    
    # Status and progress
    status = Column(String(20), default=VideoStatus.PENDING)
    progress_percentage = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    
    # Processing info
    processing_started_at = Column(DateTime(timezone=True), nullable=True)
    processing_completed_at = Column(DateTime(timezone=True), nullable=True)
    processing_time_seconds = Column(Float, nullable=True)
    
    # File info
    file_size_mb = Column(Float, nullable=True)
    video_codec = Column(String(50), nullable=True)
    audio_codec = Column(String(50), nullable=True)
    
    # Usage and sharing
    view_count = Column(Integer, default=0)
    download_count = Column(Integer, default=0)
    is_public = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)
    
    # Quality metrics
    quality_score = Column(Float, nullable=True)  # AI-generated quality score
    user_rating = Column(Integer, nullable=True)  # 1-5 stars
    
    # Additional metadata
    video_metadata = Column(JSON, default=dict)
    tags = Column(JSON, default=list)  # List of tags
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    prompt = relationship("Prompt", back_populates="videos", uselist=False)
    
    def __repr__(self):
        return f"<Video(id={self.id}, title='{self.title}', status='{self.status}')>"
    
    @property
    def is_completed(self) -> bool:
        """Check if video generation is completed"""
        return self.status == VideoStatus.COMPLETED
    
    @property
    def is_processing(self) -> bool:
        """Check if video is currently being processed"""
        return self.status in [VideoStatus.PENDING, VideoStatus.PROCESSING]
    
    @property
    def has_failed(self) -> bool:
        """Check if video generation failed"""
        return self.status == VideoStatus.FAILED
    
    @property
    def resolution_width(self) -> int:
        """Get video width from resolution string"""
        if self.resolution:
            return int(self.resolution.split('x')[0])
        return 1024
    
    @property
    def resolution_height(self) -> int:
        """Get video height from resolution string"""
        if self.resolution:
            return int(self.resolution.split('x')[1])
        return 576
    
    @property
    def estimated_processing_time(self) -> int:
        """Estimate processing time in seconds based on parameters"""
        base_time = 30  # Base processing time
        duration_factor = self.duration_seconds * 5
        resolution_factor = (self.resolution_width * self.resolution_height) / (1024 * 576)
        steps_factor = self.num_inference_steps / 25
        
        return int(base_time + duration_factor + (resolution_factor * 20) + (steps_factor * 10))
