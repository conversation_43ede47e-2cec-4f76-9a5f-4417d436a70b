from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import time

# Create FastAPI app
app = FastAPI(
    title="Video Generation API",
    description="Simple API for video generation",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Video Generation API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "message": "API is running successfully"
    }

@app.get("/api/storage/status")
async def storage_status():
    """Storage status endpoint"""
    return {
        "azure_blob_configured": True,
        "s3_configured": False,
        "primary_storage": "Azure Blob Storage",
        "message": "Storage services are configured"
    }

@app.get("/api/azure-dalle/status")
async def azure_dalle_status():
    """Azure DALL-E status endpoint"""
    return {
        "service": "Azure OpenAI DALL-E 3",
        "configured": True,
        "endpoint": "https://mentalhealth-bot.openai.azure.com",
        "deployment": "dall-e-3",
        "api_version": "2024-02-01"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
