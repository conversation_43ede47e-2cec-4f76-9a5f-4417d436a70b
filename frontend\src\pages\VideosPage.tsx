import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import LoadingSpinner from '@/components/LoadingSpinner';
import { VideoStatus } from '@/types';
import { 
  Video, 
  Eye, 
  Download, 
  Clock, 
  CheckCircle, 
  XCircle,
  Filter,
  Search,
  Grid,
  List
} from 'lucide-react';

const VideosPage: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const perPage = 12;

  const { data: videosData, isLoading, error } = useQuery({
    queryKey: ['videos', currentPage, statusFilter],
    queryFn: () => apiService.getVideos(currentPage, perPage, statusFilter || undefined),
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case VideoStatus.COMPLETED:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case VideoStatus.PROCESSING:
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case VideoStatus.PENDING:
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case VideoStatus.FAILED:
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case VideoStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case VideoStatus.PROCESSING:
        return 'bg-blue-100 text-blue-800';
      case VideoStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case VideoStatus.FAILED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredVideos = videosData?.items.filter(video =>
    video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    video.prompt_text.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading videos..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading videos</h3>
        <p className="text-gray-500">Please try again later</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Videos</h1>
          <p className="text-gray-600">
            {videosData?.total || 0} videos total
          </p>
        </div>
        <Link
          to="/generate"
          className="mt-4 sm:mt-0 btn-primary"
        >
          Generate New Video
        </Link>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search videos..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input pl-10 w-full"
              />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="input"
              >
                <option value="">All Status</option>
                <option value={VideoStatus.COMPLETED}>Completed</option>
                <option value={VideoStatus.PROCESSING}>Processing</option>
                <option value={VideoStatus.PENDING}>Pending</option>
                <option value={VideoStatus.FAILED}>Failed</option>
              </select>
            </div>
            <div className="flex items-center border border-gray-300 rounded-md">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'text-gray-500'}`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'text-gray-500'}`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Videos Grid/List */}
      {filteredVideos.length === 0 ? (
        <div className="text-center py-12">
          <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery ? 'No videos found' : 'No videos yet'}
          </h3>
          <p className="text-gray-500 mb-4">
            {searchQuery 
              ? 'Try adjusting your search terms or filters'
              : 'Create your first video to get started'
            }
          </p>
          {!searchQuery && (
            <Link to="/generate" className="btn-primary">
              Generate Your First Video
            </Link>
          )}
        </div>
      ) : (
        <>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredVideos.map((video) => (
                <Link
                  key={video.id}
                  to={`/videos/${video.id}`}
                  className="group bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <div className="aspect-video bg-gray-100 relative">
                    {video.thumbnail_url ? (
                      <img
                        src={video.thumbnail_url}
                        alt={video.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Video className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                    <div className="absolute top-2 right-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(video.status)}`}>
                        {video.status}
                      </span>
                    </div>
                    {video.status === VideoStatus.PROCESSING && (
                      <div className="absolute bottom-2 left-2 right-2">
                        <div className="bg-black bg-opacity-50 rounded-full p-1">
                          <div className="progress-bar">
                            <div 
                              className="progress-bar-fill"
                              style={{ width: `${video.progress_percentage}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-gray-900 group-hover:text-primary-600 truncate mb-2">
                      {video.title}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                      {video.prompt_text}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1">
                          <Eye className="h-3 w-3" />
                          <span>{video.view_count}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Download className="h-3 w-3" />
                          <span>{video.download_count}</span>
                        </div>
                      </div>
                      <span className="text-xs">
                        {new Date(video.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="divide-y divide-gray-200">
                {filteredVideos.map((video) => (
                  <Link
                    key={video.id}
                    to={`/videos/${video.id}`}
                    className="block hover:bg-gray-50 transition-colors"
                  >
                    <div className="p-4 flex items-center space-x-4">
                      <div className="flex-shrink-0 w-24 h-16 bg-gray-100 rounded-lg overflow-hidden">
                        {video.thumbnail_url ? (
                          <img
                            src={video.thumbnail_url}
                            alt={video.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Video className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-medium text-gray-900 truncate">
                            {video.title}
                          </h3>
                          {getStatusIcon(video.status)}
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-1 mb-2">
                          {video.prompt_text}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>{new Date(video.created_at).toLocaleDateString()}</span>
                          <div className="flex items-center space-x-1">
                            <Eye className="h-3 w-3" />
                            <span>{video.view_count}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Download className="h-3 w-3" />
                            <span>{video.download_count}</span>
                          </div>
                          {video.file_size_mb && (
                            <span>{video.file_size_mb.toFixed(1)} MB</span>
                          )}
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(video.status)}`}>
                          {video.status}
                        </span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Pagination */}
          {videosData && videosData.total_pages > 1 && (
            <div className="flex items-center justify-center space-x-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="btn-outline disabled:opacity-50"
              >
                Previous
              </button>
              <span className="text-sm text-gray-600">
                Page {currentPage} of {videosData.total_pages}
              </span>
              <button
                onClick={() => setCurrentPage(Math.min(videosData.total_pages, currentPage + 1))}
                disabled={currentPage === videosData.total_pages}
                className="btn-outline disabled:opacity-50"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default VideosPage;
