#!/bin/bash

# Video Generation Application Setup Script (Docker Only)
# This script sets up the application using only Docker containers

set -e

echo "🚀 Setting up Video Generation Application (Docker Only)..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18 or higher is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Setup environment files
setup_env_files() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f ".env" ]; then
        cp .env.example .env
        print_success "Created .env file from .env.example"
        print_warning "Please edit .env file with your API keys and configuration"
    else
        print_warning ".env file already exists, skipping..."
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        print_success "Created frontend/.env file"
    else
        print_warning "frontend/.env file already exists, skipping..."
    fi
}

# Install frontend dependencies
setup_frontend() {
    print_status "Setting up frontend dependencies..."
    
    cd frontend
    
    # Install dependencies
    npm install
    
    print_success "Frontend dependencies installed"
    cd ..
}

# Setup database and services using Docker
setup_services() {
    print_status "Setting up services with Docker..."
    
    # Start all services
    docker-compose up -d
    
    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 15
    
    # Run database migrations using Docker
    print_status "Running database migrations..."
    docker-compose exec backend alembic upgrade head
    
    print_success "Services setup completed"
}

# Create uploads directory
setup_uploads() {
    print_status "Creating uploads directory..."
    
    mkdir -p uploads/{images,videos,temp}
    chmod 755 uploads
    chmod 755 uploads/{images,videos,temp}
    
    print_success "Uploads directory created"
}

# Main setup function
main() {
    echo "🎬 Video Generation Application Setup (Docker Only)"
    echo "=================================================="
    echo ""
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    check_docker
    check_node
    
    # Setup environment
    setup_env_files
    
    # Setup frontend
    setup_frontend
    
    # Setup uploads directory
    setup_uploads
    
    # Setup services
    setup_services
    
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Edit .env file with your API keys:"
    echo "   - Google OAuth credentials"
    echo "   - OpenAI API key (optional)"
    echo "   - RunwayML API key (optional)"
    echo "   - Cloud storage credentials"
    echo ""
    echo "2. The services are now running:"
    echo "   - Frontend: http://localhost:3000"
    echo "   - Backend API: http://localhost:8000"
    echo "   - API Documentation: http://localhost:8000/docs"
    echo "   - Flower (Celery monitoring): http://localhost:5555"
    echo ""
    echo "3. To start frontend development server:"
    echo "   cd frontend && npm run dev"
    echo ""
    echo "4. To view logs:"
    echo "   docker-compose logs -f [service-name]"
    echo ""
    echo "5. To stop services:"
    echo "   docker-compose down"
    echo ""
    echo "📚 For more information, check the README.md file"
}

# Run main function
main "$@"
