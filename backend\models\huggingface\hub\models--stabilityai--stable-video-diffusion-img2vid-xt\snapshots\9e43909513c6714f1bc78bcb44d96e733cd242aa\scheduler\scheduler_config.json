{"_class_name": "EulerDiscreteScheduler", "_diffusers_version": "0.24.0.dev0", "beta_end": 0.012, "beta_schedule": "scaled_linear", "beta_start": 0.00085, "clip_sample": false, "interpolation_type": "linear", "num_train_timesteps": 1000, "prediction_type": "v_prediction", "set_alpha_to_one": false, "sigma_max": 700.0, "sigma_min": 0.002, "skip_prk_steps": true, "steps_offset": 1, "timestep_spacing": "leading", "timestep_type": "continuous", "trained_betas": null, "use_karras_sigmas": true}