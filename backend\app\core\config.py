from pydantic_settings import BaseSettings
from typing import Optional, List
import os
from pathlib import Path


class Settings(BaseSettings):
    # Application
    APP_NAME: str = "Video Generation API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # Database
    DATABASE_URL: str = "sqlite:///./videogen.db"
    DATABASE_ECHO: bool = False

    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"

    # JWT
    SECRET_KEY: str = "dev-secret-key-change-in-production-12345"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Google OAuth
    GOOGLE_CLIENT_ID: str = "placeholder-client-id"
    GOOGLE_CLIENT_SECRET: str = "placeholder-client-secret"
    GOOGLE_REDIRECT_URI: str = "http://localhost:3000/auth/callback"
    
    # AI Model APIs
    OPENAI_API_KEY: Optional[str] = None
    AZURE_OPENAI_API_KEY: Optional[str] = None
    AZURE_OPENAI_ENDPOINT: Optional[str] = None
    AZURE_OPENAI_API_VERSION: str = "2024-02-01"
    AZURE_DALLE_DEPLOYMENT_NAME: str = "dall-e-3"
    RUNWAYML_API_KEY: Optional[str] = None
    HUGGINGFACE_API_KEY: Optional[str] = None
    STABILITY_API_KEY: Optional[str] = None
    
    # Cloud Storage (AWS S3)
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_BUCKET_NAME: Optional[str] = None
    AWS_REGION: str = "us-east-1"

    # Azure Blob Storage
    BLOB_SAS_TOKEN: Optional[str] = None
    BLOB_SAS_URL: Optional[str] = None
    PDF_BUCKET_NAME: Optional[str] = None
    
    # CloudFlare R2 (Alternative)
    CLOUDFLARE_R2_ACCESS_KEY_ID: Optional[str] = None
    CLOUDFLARE_R2_SECRET_ACCESS_KEY: Optional[str] = None
    CLOUDFLARE_R2_BUCKET_NAME: Optional[str] = None
    CLOUDFLARE_R2_ENDPOINT: Optional[str] = None
    
    # URLs
    FRONTEND_URL: str = "http://localhost:3000"
    BACKEND_URL: str = "http://localhost:8000"
    
    # File Upload
    MAX_FILE_SIZE_MB: int = 100
    ALLOWED_IMAGE_EXTENSIONS: List[str] = ["jpg", "jpeg", "png", "gif", "webp"]
    ALLOWED_VIDEO_EXTENSIONS: List[str] = ["mp4", "avi", "mov", "webm"]
    UPLOAD_DIR: str = "uploads"

    # Local Model Storage Configuration
    MODELS_CACHE_DIR: Optional[str] = "./models"
    HF_HOME: Optional[str] = "./models/huggingface"
    TRANSFORMERS_CACHE: Optional[str] = "./models/transformers"
    DIFFUSERS_CACHE: Optional[str] = "./models/diffusers"
    
    # Video Generation
    MAX_VIDEO_DURATION_SECONDS: int = 30
    DEFAULT_VIDEO_RESOLUTION: str = "1024x576"
    MAX_CONCURRENT_GENERATIONS: int = 5
    
    # Email (Optional)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # Monitoring
    LOG_LEVEL: str = "INFO"
    SENTRY_DSN: Optional[str] = None
    
    # CORS
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://localhost:3000"
    ]
    
    # Celery
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    @property
    def max_file_size_bytes(self) -> int:
        return self.MAX_FILE_SIZE_MB * 1024 * 1024
    
    @property
    def upload_path(self) -> Path:
        return Path(self.UPLOAD_DIR)
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()

# Ensure upload directory exists
settings.upload_path.mkdir(exist_ok=True)
(settings.upload_path / "images").mkdir(exist_ok=True)
(settings.upload_path / "videos").mkdir(exist_ok=True)
(settings.upload_path / "temp").mkdir(exist_ok=True)
