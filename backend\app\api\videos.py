from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
import logging

from app.core.database import get_db
from app.core.security import get_current_user_token
from app.models.user import User
from app.models.video import Video, VideoStatus, VideoModel
from app.models.prompt import Prompt

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models
class VideoGenerationRequest(BaseModel):
    title: str
    prompt_text: str
    negative_prompt: Optional[str] = None
    input_image_url: Optional[str] = None
    model: VideoModel = VideoModel.STABLE_VIDEO_DIFFUSION
    resolution: str = "1024x576"
    duration_seconds: float = 4.0
    guidance_scale: float = 7.5
    num_inference_steps: int = 25
    seed: Optional[int] = None


class VideoResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    prompt_text: str
    status: str
    progress_percentage: int
    output_video_url: Optional[str]
    thumbnail_url: Optional[str]
    duration_seconds: float
    resolution: str
    file_size_mb: Optional[float]
    created_at: datetime
    processing_time_seconds: Optional[float]

    class Config:
        from_attributes = True


class VideoListResponse(BaseModel):
    videos: List[VideoResponse]
    total: int
    page: int
    per_page: int


@router.post("/generate", response_model=VideoResponse)
async def generate_video(
    request: VideoGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Generate a new video from prompt"""
    try:
        # Get user
        user = db.query(User).filter(User.id == current_user["user_id"]).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check if user can generate videos
        if not user.can_generate_video:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Video generation limit reached. Upgrade to premium for unlimited generations."
            )
        
        # Create video record
        video = Video(
            user_id=user.id,
            title=request.title,
            prompt_text=request.prompt_text,
            negative_prompt=request.negative_prompt,
            input_image_url=request.input_image_url,
            model_used=request.model,
            resolution=request.resolution,
            duration_seconds=request.duration_seconds,
            guidance_scale=request.guidance_scale,
            num_inference_steps=request.num_inference_steps,
            seed=request.seed,
            status=VideoStatus.PENDING
        )
        
        db.add(video)
        db.commit()
        db.refresh(video)
        
        # Save or update prompt
        prompt = db.query(Prompt).filter(
            Prompt.user_id == user.id,
            Prompt.prompt_text == request.prompt_text
        ).first()
        
        if not prompt:
            prompt = Prompt(
                user_id=user.id,
                title=request.title,
                prompt_text=request.prompt_text,
                negative_prompt=request.negative_prompt,
                default_model=request.model,
                default_resolution=request.resolution,
                default_duration=request.duration_seconds
            )
            db.add(prompt)
        else:
            prompt.increment_usage()
        
        db.commit()
        
        # Start video generation in background
        from app.services.video_generation import generate_video_task
        background_tasks.add_task(generate_video_task, video.id)
        
        logger.info(f"Video generation started: {video.id} for user {user.email}")
        
        return video
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Video generation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start video generation"
        )


@router.get("/", response_model=VideoListResponse)
async def list_videos(
    page: int = 1,
    per_page: int = 20,
    status_filter: Optional[str] = None,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """List user's videos with pagination"""
    try:
        # Build query
        query = db.query(Video).filter(Video.user_id == current_user["user_id"])
        
        if status_filter:
            query = query.filter(Video.status == status_filter)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * per_page
        videos = query.order_by(Video.created_at.desc()).offset(offset).limit(per_page).all()
        
        return VideoListResponse(
            videos=videos,
            total=total,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        logger.error(f"List videos error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve videos"
        )


@router.get("/{video_id}", response_model=VideoResponse)
async def get_video(
    video_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Get specific video details"""
    video = db.query(Video).filter(
        Video.id == video_id,
        Video.user_id == current_user["user_id"]
    ).first()
    
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    # Increment view count
    video.view_count += 1
    db.commit()
    
    return video


@router.delete("/{video_id}")
async def delete_video(
    video_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Delete a video"""
    video = db.query(Video).filter(
        Video.id == video_id,
        Video.user_id == current_user["user_id"]
    ).first()
    
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    # Delete files if they exist
    import os
    if video.output_video_path and os.path.exists(video.output_video_path):
        os.remove(video.output_video_path)
    if video.thumbnail_path and os.path.exists(video.thumbnail_path):
        os.remove(video.thumbnail_path)
    
    # Update user storage
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if user and video.file_size_mb:
        user.storage_used_mb = max(0, user.storage_used_mb - video.file_size_mb)
    
    # Delete video record
    db.delete(video)
    db.commit()
    
    logger.info(f"Video deleted: {video_id} by user {current_user['email']}")
    
    return {"message": "Video deleted successfully"}


@router.post("/{video_id}/download")
async def download_video(
    video_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Get download URL for video"""
    video = db.query(Video).filter(
        Video.id == video_id,
        Video.user_id == current_user["user_id"]
    ).first()
    
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    if video.status != VideoStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Video is not ready for download"
        )
    
    # Increment download count
    video.download_count += 1
    db.commit()
    
    return {
        "download_url": video.output_video_url,
        "filename": f"{video.title}.mp4",
        "file_size_mb": video.file_size_mb
    }
