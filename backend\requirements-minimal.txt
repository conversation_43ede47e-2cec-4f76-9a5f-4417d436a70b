# Essential packages for basic functionality
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database (SQLite for development)
sqlalchemy==2.0.23
aiosqlite==0.19.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
google-auth==2.23.4
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.2.0

# Data Validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP Clients
httpx==0.25.2
requests==2.31.0

# Azure Services
azure-storage-blob==12.19.0

# File Processing
Pillow==10.1.0

# Environment
python-dotenv==1.0.0

# Development Tools
rich==13.7.0

# Optional: Production packages (uncomment if needed)
# gunicorn==21.2.0
# psycopg2-binary==2.9.9
# asyncpg==0.29.0
# redis==4.6.0
# celery[redis]==5.3.4
# flower==2.0.1
# boto3==1.34.0
# botocore==1.34.0
