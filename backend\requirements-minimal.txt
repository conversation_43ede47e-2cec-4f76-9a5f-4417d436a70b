# Essential packages for basic functionality
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Cache and Background Tasks
redis==4.6.0
celery[redis]==5.3.4
flower==2.0.1

# Authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
google-auth==2.23.4
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.2.0

# Data Validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP Clients
httpx==0.25.2
aiohttp==3.9.1

# File Storage
boto3==1.34.0
botocore==1.34.0
# python-magic==0.4.27  # Removed due to Windows compatibility issues
Pillow==10.1.0

# Environment
python-dotenv==1.0.0

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
