import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import Base, get_db
from app.core.config import settings
from app.models.user import User
from app.models.video import Video
from app.models.prompt import Prompt

# Test database URL
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a fresh database session for each test."""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client():
    """Create a test client."""
    with TestClient(app) as c:
        yield c


@pytest.fixture
def test_user(db_session):
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        full_name="Test User",
        google_id="test_google_id",
        google_verified=True,
        is_active=True,
        is_verified=True,
        is_premium=False,
        subscription_type="free",
        videos_generated=0,
        storage_used_mb=0,
        api_calls_count=0,
        api_calls_limit=100
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def premium_user(db_session):
    """Create a premium test user."""
    user = User(
        email="<EMAIL>",
        full_name="Premium User",
        google_id="premium_google_id",
        google_verified=True,
        is_active=True,
        is_verified=True,
        is_premium=True,
        subscription_type="premium",
        videos_generated=0,
        storage_used_mb=0,
        api_calls_count=0,
        api_calls_limit=1000
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_video(db_session, test_user):
    """Create a test video."""
    video = Video(
        user_id=test_user.id,
        title="Test Video",
        prompt_text="A test video prompt",
        model_used="stable_video_diffusion",
        resolution="1024x576",
        duration_seconds=4.0,
        status="completed"
    )
    db_session.add(video)
    db_session.commit()
    db_session.refresh(video)
    return video


@pytest.fixture
def test_prompt(db_session, test_user):
    """Create a test prompt."""
    prompt = Prompt(
        user_id=test_user.id,
        title="Test Prompt",
        prompt_text="A beautiful landscape with mountains and lakes",
        category="nature",
        style="realistic",
        is_public=True
    )
    db_session.add(prompt)
    db_session.commit()
    db_session.refresh(prompt)
    return prompt


@pytest.fixture
def auth_headers(test_user):
    """Create authentication headers for test user."""
    from app.core.security import create_access_token
    
    token_data = {"sub": str(test_user.id), "email": test_user.email}
    access_token = create_access_token(token_data)
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def premium_auth_headers(premium_user):
    """Create authentication headers for premium user."""
    from app.core.security import create_access_token
    
    token_data = {"sub": str(premium_user.id), "email": premium_user.email}
    access_token = create_access_token(token_data)
    
    return {"Authorization": f"Bearer {access_token}"}
