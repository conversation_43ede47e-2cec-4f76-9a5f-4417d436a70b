from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, JSON, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class Prompt(Base):
    __tablename__ = "prompts"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # User relationship
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    user = relationship("User", back_populates="prompts")
    
    # Prompt content
    title = Column(String(255), nullable=False)
    prompt_text = Column(Text, nullable=False)
    negative_prompt = Column(Text, nullable=True)
    
    # Prompt metadata
    category = Column(String(100), nullable=True)  # e.g., "nature", "abstract", "portrait"
    style = Column(String(100), nullable=True)     # e.g., "cinematic", "anime", "realistic"
    mood = Column(String(100), nullable=True)      # e.g., "dramatic", "peaceful", "energetic"
    
    # Usage statistics
    use_count = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)  # Percentage of successful generations
    average_rating = Column(Float, default=0.0)
    
    # Sharing and visibility
    is_public = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)
    is_template = Column(Boolean, default=False)
    
    # Quality metrics
    quality_score = Column(Float, nullable=True)
    complexity_score = Column(Float, nullable=True)  # How complex/detailed the prompt is
    
    # Tags and categorization
    tags = Column(JSON, default=list)  # List of tags
    keywords = Column(JSON, default=list)  # Extracted keywords
    
    # Generation parameters (defaults for this prompt)
    default_model = Column(String(100), nullable=True)
    default_resolution = Column(String(20), default="1024x576")
    default_duration = Column(Float, default=4.0)
    default_guidance_scale = Column(Float, default=7.5)
    default_steps = Column(Integer, default=25)
    
    # Additional metadata
    prompt_metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="prompts")
    # videos = relationship("Video", back_populates="prompt")  # Temporarily disabled
    
    def __repr__(self):
        return f"<Prompt(id={self.id}, title='{self.title}', use_count={self.use_count})>"
    
    @property
    def is_popular(self) -> bool:
        """Check if prompt is popular (used frequently)"""
        return self.use_count > 10
    
    @property
    def is_high_quality(self) -> bool:
        """Check if prompt produces high-quality results"""
        return self.success_rate > 0.8 and self.average_rating > 4.0
    
    @property
    def word_count(self) -> int:
        """Get word count of the prompt"""
        return len(self.prompt_text.split())
    
    def increment_usage(self):
        """Increment usage count and update last used timestamp"""
        self.use_count += 1
        self.last_used_at = func.now()
    
    def update_success_rate(self, successful_generations: int, total_generations: int):
        """Update success rate based on generation results"""
        if total_generations > 0:
            self.success_rate = successful_generations / total_generations
    
    def update_average_rating(self, new_rating: float, total_ratings: int):
        """Update average rating with new rating"""
        if total_ratings > 0:
            current_total = self.average_rating * (total_ratings - 1)
            self.average_rating = (current_total + new_rating) / total_ratings
