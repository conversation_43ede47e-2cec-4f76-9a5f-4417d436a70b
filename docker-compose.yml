version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: videogen_postgres
    environment:
      POSTGRES_DB: videogen_db
      POSTGRES_USER: videogen_user
      POSTGRES_PASSWORD: videogen_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - videogen_network

  # Redis for caching and job queue
  redis:
    image: redis:7-alpine
    container_name: videogen_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - videogen_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: videogen_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************************************/videogen_db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - videogen_network
    restart: unless-stopped

  # Celery Worker for background tasks
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: videogen_celery_worker
    command: celery -A app.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=**********************************************************/videogen_db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - videogen_network
    restart: unless-stopped

  # Celery Beat for scheduled tasks
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: videogen_celery_beat
    command: celery -A app.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=**********************************************************/videogen_db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
    networks:
      - videogen_network
    restart: unless-stopped

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: videogen_frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
    depends_on:
      - backend
    networks:
      - videogen_network
    restart: unless-stopped

  # Flower for monitoring Celery tasks
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: videogen_flower
    command: celery -A app.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - DATABASE_URL=**********************************************************/videogen_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    networks:
      - videogen_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  videogen_network:
    driver: bridge
