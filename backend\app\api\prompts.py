from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List
import logging

from app.core.database import get_db
from app.core.security import get_current_user_token
from app.models.prompt import Prompt
from app.models.user import User

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models
class PromptCreate(BaseModel):
    title: str
    prompt_text: str
    negative_prompt: Optional[str] = None
    category: Optional[str] = None
    style: Optional[str] = None
    mood: Optional[str] = None
    tags: List[str] = []
    is_public: bool = False


class PromptUpdate(BaseModel):
    title: Optional[str] = None
    prompt_text: Optional[str] = None
    negative_prompt: Optional[str] = None
    category: Optional[str] = None
    style: Optional[str] = None
    mood: Optional[str] = None
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None


class PromptResponse(BaseModel):
    id: int
    title: str
    prompt_text: str
    negative_prompt: Optional[str]
    category: Optional[str]
    style: Optional[str]
    mood: Optional[str]
    tags: List[str]
    use_count: int
    success_rate: float
    average_rating: float
    is_public: bool
    created_at: str
    
    class Config:
        from_attributes = True


class PromptListResponse(BaseModel):
    prompts: List[PromptResponse]
    total: int
    page: int
    per_page: int


@router.post("/", response_model=PromptResponse)
async def create_prompt(
    prompt_data: PromptCreate,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Create a new prompt"""
    try:
        prompt = Prompt(
            user_id=current_user["user_id"],
            title=prompt_data.title,
            prompt_text=prompt_data.prompt_text,
            negative_prompt=prompt_data.negative_prompt,
            category=prompt_data.category,
            style=prompt_data.style,
            mood=prompt_data.mood,
            tags=prompt_data.tags,
            is_public=prompt_data.is_public
        )
        
        db.add(prompt)
        db.commit()
        db.refresh(prompt)
        
        logger.info(f"Prompt created: {prompt.id} by user {current_user['email']}")
        return prompt
        
    except Exception as e:
        logger.error(f"Create prompt error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create prompt"
        )


@router.get("/", response_model=PromptListResponse)
async def list_prompts(
    page: int = 1,
    per_page: int = 20,
    category: Optional[str] = None,
    style: Optional[str] = None,
    public_only: bool = False,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """List prompts with filtering"""
    try:
        # Build query
        if public_only:
            query = db.query(Prompt).filter(Prompt.is_public == True)
        else:
            query = db.query(Prompt).filter(Prompt.user_id == current_user["user_id"])
        
        # Apply filters
        if category:
            query = query.filter(Prompt.category == category)
        if style:
            query = query.filter(Prompt.style == style)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * per_page
        prompts = query.order_by(Prompt.created_at.desc()).offset(offset).limit(per_page).all()
        
        return PromptListResponse(
            prompts=prompts,
            total=total,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        logger.error(f"List prompts error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve prompts"
        )


@router.get("/popular", response_model=List[PromptResponse])
async def get_popular_prompts(
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """Get popular public prompts"""
    try:
        prompts = db.query(Prompt).filter(
            Prompt.is_public == True
        ).order_by(
            Prompt.use_count.desc(),
            Prompt.average_rating.desc()
        ).limit(limit).all()
        
        return prompts
        
    except Exception as e:
        logger.error(f"Get popular prompts error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve popular prompts"
        )


@router.get("/categories")
async def get_prompt_categories(db: Session = Depends(get_db)):
    """Get available prompt categories"""
    try:
        from sqlalchemy import func, distinct
        
        categories = db.query(distinct(Prompt.category)).filter(
            Prompt.category.isnot(None),
            Prompt.is_public == True
        ).all()
        
        styles = db.query(distinct(Prompt.style)).filter(
            Prompt.style.isnot(None),
            Prompt.is_public == True
        ).all()
        
        moods = db.query(distinct(Prompt.mood)).filter(
            Prompt.mood.isnot(None),
            Prompt.is_public == True
        ).all()
        
        return {
            "categories": [cat[0] for cat in categories if cat[0]],
            "styles": [style[0] for style in styles if style[0]],
            "moods": [mood[0] for mood in moods if mood[0]]
        }
        
    except Exception as e:
        logger.error(f"Get categories error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve categories"
        )


@router.get("/{prompt_id}", response_model=PromptResponse)
async def get_prompt(
    prompt_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Get specific prompt"""
    prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
    
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Prompt not found"
        )
    
    # Check access permissions
    if not prompt.is_public and prompt.user_id != current_user["user_id"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return prompt


@router.put("/{prompt_id}", response_model=PromptResponse)
async def update_prompt(
    prompt_id: int,
    prompt_update: PromptUpdate,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Update a prompt"""
    prompt = db.query(Prompt).filter(
        Prompt.id == prompt_id,
        Prompt.user_id == current_user["user_id"]
    ).first()
    
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Prompt not found"
        )
    
    # Update fields
    for field, value in prompt_update.dict(exclude_unset=True).items():
        setattr(prompt, field, value)
    
    db.commit()
    db.refresh(prompt)
    
    logger.info(f"Prompt updated: {prompt_id} by user {current_user['email']}")
    return prompt


@router.delete("/{prompt_id}")
async def delete_prompt(
    prompt_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Delete a prompt"""
    prompt = db.query(Prompt).filter(
        Prompt.id == prompt_id,
        Prompt.user_id == current_user["user_id"]
    ).first()
    
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Prompt not found"
        )
    
    db.delete(prompt)
    db.commit()
    
    logger.info(f"Prompt deleted: {prompt_id} by user {current_user['email']}")
    return {"message": "Prompt deleted successfully"}
