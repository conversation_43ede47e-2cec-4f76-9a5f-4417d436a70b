import os
import uuid
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional
import asyncio
from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.core.config import settings
from app.models.video import Video, VideoStatus, VideoModel
from app.models.user import User
from app.services.azure_openai import azure_openai_service

logger = logging.getLogger(__name__)

# Configure local model storage
MODELS_DIR = Path(__file__).parent.parent.parent / "models"
MODELS_DIR.mkdir(exist_ok=True)

# Set environment variables for local model storage
os.environ["HF_HOME"] = str(MODELS_DIR / "huggingface")
os.environ["TRANSFORMERS_CACHE"] = str(MODELS_DIR / "transformers")
os.environ["DIFFUSERS_CACHE"] = str(MODELS_DIR / "diffusers")

# Create subdirectories
for cache_dir in ["huggingface", "transformers", "diffusers"]:
    (MODELS_DIR / cache_dir).mkdir(exist_ok=True)

logger.info(f"Models will be stored locally in: {MODELS_DIR}")


def create_mock_video_files(video_id: int, prompt: str) -> tuple[str, str, float]:
    """Create actual mock video and thumbnail files for testing"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import cv2
        import numpy as np

        # Create output directories
        video_dir = Path(settings.upload_path) / "videos"
        video_dir.mkdir(parents=True, exist_ok=True)

        # File paths
        video_filename = f"mock_video_{video_id}.mp4"
        thumbnail_filename = f"mock_thumb_{video_id}.jpg"
        video_path = video_dir / video_filename
        thumbnail_path = video_dir / thumbnail_filename

        # Create a simple video with text
        width, height = 1024, 576
        fps = 24
        duration = 4  # seconds
        total_frames = fps * duration

        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(str(video_path), fourcc, fps, (width, height))

        # Generate frames
        for frame_num in range(total_frames):
            # Create a frame with gradient background
            frame = np.zeros((height, width, 3), dtype=np.uint8)

            # Create gradient effect
            for y in range(height):
                color_intensity = int(255 * (y / height))
                frame[y, :] = [color_intensity // 3, color_intensity // 2, color_intensity]

            # Add text overlay
            frame_pil = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(frame_pil)

            # Try to use a font, fallback to default if not available
            try:
                font = ImageFont.truetype("arial.ttf", 40)
            except:
                font = ImageFont.load_default()

            # Add prompt text
            text_lines = [
                f"Mock Video #{video_id}",
                f"Prompt: {prompt[:50]}{'...' if len(prompt) > 50 else ''}",
                f"Frame: {frame_num + 1}/{total_frames}",
                f"Time: {frame_num / fps:.1f}s"
            ]

            y_offset = 50
            for line in text_lines:
                draw.text((50, y_offset), line, fill=(255, 255, 255), font=font)
                y_offset += 60

            # Convert back to OpenCV format
            frame = cv2.cvtColor(np.array(frame_pil), cv2.COLOR_RGB2BGR)
            video_writer.write(frame)

        video_writer.release()

        # Create thumbnail (first frame)
        cap = cv2.VideoCapture(str(video_path))
        ret, frame = cap.read()
        if ret:
            cv2.imwrite(str(thumbnail_path), frame)
        cap.release()

        # Get file size
        file_size_mb = video_path.stat().st_size / (1024 * 1024)

        logger.info(f"Created mock video: {video_path} ({file_size_mb:.2f} MB)")
        return str(video_path), str(thumbnail_path), file_size_mb

    except Exception as e:
        logger.error(f"Error creating mock video files: {e}")
        # Fallback to simple text files
        video_path = video_dir / f"mock_video_{video_id}.txt"
        thumbnail_path = video_dir / f"mock_thumb_{video_id}.txt"

        with open(video_path, 'w') as f:
            f.write(f"Mock video for prompt: {prompt}\nVideo ID: {video_id}")

        with open(thumbnail_path, 'w') as f:
            f.write(f"Mock thumbnail for video {video_id}")

        return str(video_path), str(thumbnail_path), 0.1

    def create_thumbnail(self, video_path: str) -> str:
        """Create thumbnail from video file"""
        try:
            import cv2
            from pathlib import Path

            video_path_obj = Path(video_path)
            thumbnail_path = video_path_obj.parent / f"{video_path_obj.stem}_thumb.jpg"

            # Extract first frame as thumbnail
            cap = cv2.VideoCapture(video_path)
            ret, frame = cap.read()
            if ret:
                cv2.imwrite(str(thumbnail_path), frame)
            cap.release()

            return str(thumbnail_path)
        except Exception as e:
            logger.error(f"Error creating thumbnail: {e}")
            # Return a default thumbnail path
            return str(Path(video_path).parent / "default_thumb.jpg")


class VideoGenerationService:
    """Service for handling video generation with different AI models"""
    
    def __init__(self):
        self.output_dir = settings.upload_path / "videos"
        self.temp_dir = settings.upload_path / "temp"
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
    
    async def generate_with_stable_video_diffusion(
        self, 
        prompt: str, 
        negative_prompt: Optional[str] = None,
        input_image_path: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate video using Stable Video Diffusion"""
        try:
            # Import here to avoid loading heavy dependencies at startup
            import torch
            from diffusers import StableVideoDiffusionPipeline
            from PIL import Image
            import imageio
            
            logger.info("Loading Stable Video Diffusion model...")
            
            # Load the model with local cache and ensure complete download
            model_name = "stabilityai/stable-video-diffusion-img2vid-xt"
            logger.info(f"Loading model: {model_name}")
            logger.info(f"Cache directory: {MODELS_DIR / 'diffusers'}")

            pipe = StableVideoDiffusionPipeline.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                variant="fp16",
                cache_dir=str(MODELS_DIR / "diffusers"),
                local_files_only=False,  # Allow download if not cached locally
                resume_download=True,    # Resume interrupted downloads
                force_download=False,    # Don't re-download existing files
                use_safetensors=True     # Use safetensors format when available
            )

            logger.info("Model loaded successfully from local cache")
            
            # Move to GPU if available
            if torch.cuda.is_available():
                pipe = pipe.to("cuda")
                logger.info("Using GPU for video generation")
            else:
                logger.info("Using CPU for video generation")
            
            # Prepare input image
            if input_image_path and os.path.exists(input_image_path):
                image = Image.open(input_image_path).convert("RGB")
                image = image.resize((1024, 576))
            else:
                # Generate image from prompt first using DALL-E or Stable Diffusion
                image = await self._generate_image_from_prompt(prompt)
            
            # Generate video frames
            logger.info("Generating video frames...")
            frames = pipe(
                image=image,
                decode_chunk_size=8,
                generator=torch.manual_seed(kwargs.get('seed', 42)),
                motion_bucket_id=127,
                noise_aug_strength=0.02,
                num_frames=kwargs.get('num_frames', 25)
            ).frames[0]
            
            # Save video
            output_filename = f"{uuid.uuid4()}.mp4"
            output_path = self.output_dir / output_filename
            
            # Convert frames to video
            imageio.mimsave(
                str(output_path),
                frames,
                fps=kwargs.get('fps', 7),
                quality=8
            )
            
            logger.info(f"Video saved to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Stable Video Diffusion generation error: {e}")
            raise
    
    async def generate_with_runwayml(
        self, 
        prompt: str, 
        input_image_url: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate video using RunwayML API"""
        try:
            import httpx
            
            if not settings.RUNWAYML_API_KEY:
                raise ValueError("RunwayML API key not configured")
            
            logger.info("Generating video with RunwayML...")
            
            # Prepare request data
            request_data = {
                "text_prompt": prompt,
                "model": "gen3a_turbo",
                "aspect_ratio": "16:9",
                "duration": kwargs.get('duration_seconds', 5)
            }
            
            if input_image_url:
                request_data["image_prompt"] = input_image_url
            
            # Make API request
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.runwayml.com/v1/image_to_video",
                    headers={
                        "Authorization": f"Bearer {settings.RUNWAYML_API_KEY}",
                        "Content-Type": "application/json"
                    },
                    json=request_data,
                    timeout=300
                )
                
                if response.status_code != 200:
                    raise Exception(f"RunwayML API error: {response.text}")
                
                result = response.json()
                task_id = result.get("id")
                
                # Poll for completion
                video_url = await self._poll_runwayml_task(client, task_id)
                
                # Download video
                output_path = await self._download_video(client, video_url)
                
                logger.info(f"RunwayML video saved to: {output_path}")
                return output_path
                
        except Exception as e:
            logger.error(f"RunwayML generation error: {e}")
            raise
    
    async def _generate_image_from_prompt(self, prompt: str) -> "PIL.Image":
        """Generate image from text prompt using Stable Diffusion (local), Azure DALL-E, or OpenAI DALL-E"""
        try:
            # Try local Stable Diffusion first (offline, fast)
            logger.info(f"Generating image with local Stable Diffusion: {prompt[:50]}...")
            return await self._generate_with_stable_diffusion(prompt)

        except Exception as e:
            logger.error(f"Local Stable Diffusion generation error: {e}")

            # Try Azure OpenAI as fallback
            try:
                if settings.AZURE_OPENAI_API_KEY and settings.AZURE_OPENAI_ENDPOINT:
                    logger.info(f"Falling back to Azure DALL-E 3: {prompt[:50]}...")
                    return await self._generate_with_azure_dalle(prompt)
            except Exception as e2:
                logger.error(f"Azure DALL-E 3 fallback error: {e2}")

            # Try OpenAI DALL-E as final fallback
            try:
                if settings.OPENAI_API_KEY:
                    logger.info(f"Falling back to OpenAI DALL-E: {prompt[:50]}...")
                    return await self._generate_with_dalle(prompt)
            except Exception as e3:
                logger.error(f"OpenAI DALL-E fallback error: {e3}")

            # If all methods fail, raise the original error
            logger.error(f"All image generation methods failed. Original error: {e}")
            raise

    async def _generate_with_azure_dalle(self, prompt: str) -> "PIL.Image":
        """Generate image using Azure OpenAI DALL-E 3"""
        from PIL import Image

        try:
            # Generate image using Azure OpenAI service
            result = await azure_openai_service.generate_image_for_video(
                prompt=prompt,
                video_aspect_ratio="16:9"
            )

            # Load the saved image
            image_path = result["local_path"]
            image = Image.open(image_path).convert("RGB")

            logger.info(f"Generated image with Azure DALL-E 3: {result['revised_prompt']}")
            return image

        except Exception as e:
            logger.error(f"Azure DALL-E 3 generation error: {e}")
            raise

    async def _generate_with_dalle(self, prompt: str) -> "PIL.Image":
        """Generate image using DALL-E"""
        import openai
        from PIL import Image
        import httpx
        
        client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
        response = await client.images.generate(
            model="dall-e-3",
            prompt=prompt,
            size="1024x1024",
            quality="standard",
            n=1,
        )
        
        image_url = response.data[0].url
        
        # Download image
        async with httpx.AsyncClient() as http_client:
            img_response = await http_client.get(image_url)
            img_response.raise_for_status()
            
            temp_path = self.temp_dir / f"{uuid.uuid4()}.png"
            with open(temp_path, "wb") as f:
                f.write(img_response.content)
            
            return Image.open(temp_path).convert("RGB")
    
    async def _generate_with_stable_diffusion(self, prompt: str) -> "PIL.Image":
        """Generate image using Stable Diffusion"""
        import torch
        from diffusers import StableDiffusionPipeline
        
        pipe = StableDiffusionPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5",
            torch_dtype=torch.float16,
            cache_dir=str(MODELS_DIR / "diffusers"),
            local_files_only=False,
            resume_download=True,
            use_safetensors=True
        )
        
        if torch.cuda.is_available():
            pipe = pipe.to("cuda")
        
        image = pipe(prompt).images[0]
        return image
    
    async def _poll_runwayml_task(self, client: "httpx.AsyncClient", task_id: str) -> str:
        """Poll RunwayML task until completion"""
        import asyncio
        
        max_attempts = 60  # 5 minutes with 5-second intervals
        attempt = 0
        
        while attempt < max_attempts:
            response = await client.get(
                f"https://api.runwayml.com/v1/tasks/{task_id}",
                headers={"Authorization": f"Bearer {settings.RUNWAYML_API_KEY}"}
            )
            
            if response.status_code != 200:
                raise Exception(f"RunwayML polling error: {response.text}")
            
            result = response.json()
            status = result.get("status")
            
            if status == "SUCCEEDED":
                return result.get("output", {}).get("video_url")
            elif status == "FAILED":
                raise Exception(f"RunwayML generation failed: {result.get('error')}")
            
            await asyncio.sleep(5)
            attempt += 1
        
        raise Exception("RunwayML generation timeout")
    
    async def _download_video(self, client: "httpx.AsyncClient", video_url: str) -> str:
        """Download video from URL"""
        response = await client.get(video_url)
        response.raise_for_status()
        
        output_filename = f"{uuid.uuid4()}.mp4"
        output_path = self.output_dir / output_filename
        
        with open(output_path, "wb") as f:
            f.write(response.content)
        
        return str(output_path)
    
    def create_thumbnail(self, video_path: str) -> str:
        """Create thumbnail from video"""
        try:
            import cv2
            
            # Open video
            cap = cv2.VideoCapture(video_path)
            
            # Read first frame
            ret, frame = cap.read()
            if not ret:
                raise Exception("Could not read video frame")
            
            # Save thumbnail
            thumbnail_filename = f"{uuid.uuid4()}_thumb.jpg"
            thumbnail_path = self.output_dir / thumbnail_filename
            
            cv2.imwrite(str(thumbnail_path), frame)
            cap.release()
            
            return str(thumbnail_path)
            
        except Exception as e:
            logger.error(f"Thumbnail creation error: {e}")
            return ""


# Background task function
async def generate_video_task(video_id: int):
    """Background task to generate video (Mock Implementation)"""
    db = SessionLocal()

    try:
        # Get video record
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            logger.error(f"Video not found: {video_id}")
            return

        # Update status
        video.status = VideoStatus.PROCESSING
        video.processing_started_at = datetime.utcnow()
        db.commit()

        logger.info(f"Starting video generation for video {video_id}")

        # Real AI video generation
        import time
        import random
        from pathlib import Path

        # Try real AI generation first, fallback to mock if needed
        try:
            if video.model_used == VideoModel.STABLE_VIDEO_DIFFUSION:
                # Use real Stable Video Diffusion
                service = VideoGenerationService()
                output_path = await service.generate_with_stable_video_diffusion(
                    prompt=video.prompt_text,
                    negative_prompt=video.negative_prompt,
                    duration_seconds=video.duration_seconds,
                    seed=video.seed
                )

                # Create thumbnail
                thumbnail_path = service.create_thumbnail(output_path)
                file_size_mb = Path(output_path).stat().st_size / (1024 * 1024)

                mock_video_path = output_path
                mock_thumbnail_path = thumbnail_path
                mock_file_size = file_size_mb

            elif video.model_used == VideoModel.RUNWAYML:
                # Use RunwayML API
                service = VideoGenerationService()
                output_path = await service.generate_with_runwayml(
                    prompt=video.prompt_text,
                    duration_seconds=video.duration_seconds
                )

                # Create thumbnail
                thumbnail_path = service.create_thumbnail(output_path)
                file_size_mb = Path(output_path).stat().st_size / (1024 * 1024)

                mock_video_path = output_path
                mock_thumbnail_path = thumbnail_path
                mock_file_size = file_size_mb
            else:
                raise ValueError(f"Unsupported model: {video.model_used}")

        except Exception as e:
            logger.warning(f"AI generation failed, using mock: {e}")
            # Fallback to mock generation
            processing_time = random.uniform(2, 8)
            time.sleep(processing_time)
            mock_video_path, mock_thumbnail_path, mock_file_size = create_mock_video_files(video_id, video.prompt_text)
        
        # Update video record with mock data
        video.status = VideoStatus.COMPLETED
        video.output_video_path = mock_video_path
        video.output_video_url = f"{settings.BACKEND_URL}/uploads/videos/{Path(mock_video_path).name}"
        video.thumbnail_path = mock_thumbnail_path
        video.thumbnail_url = f"{settings.BACKEND_URL}/uploads/videos/{Path(mock_thumbnail_path).name}" if mock_thumbnail_path else None
        video.file_size_mb = mock_file_size
        video.processing_completed_at = datetime.utcnow()
        video.processing_time_seconds = (video.processing_completed_at - video.processing_started_at).total_seconds()
        video.progress_percentage = 100

        # Update user stats
        user = db.query(User).filter(User.id == video.user_id).first()
        if user:
            user.videos_generated += 1
            user.storage_used_mb += mock_file_size
        
        db.commit()
        
        logger.info(f"Video generation completed for video {video_id}")
        
    except Exception as e:
        logger.error(f"Video generation failed for video {video_id}: {e}")
        
        # Update video status to failed
        video = db.query(Video).filter(Video.id == video_id).first()
        if video:
            video.status = VideoStatus.FAILED
            video.error_message = str(e)
            video.processing_completed_at = datetime.utcnow()
            if video.processing_started_at:
                video.processing_time_seconds = (video.processing_completed_at - video.processing_started_at).total_seconds()
            db.commit()
    
    finally:
        db.close()
