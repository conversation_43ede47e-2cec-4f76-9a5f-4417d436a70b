import os
import uuid
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional
import asyncio
from sqlalchemy.orm import Session

from app.core.database import SessionLocal
from app.core.config import settings
from app.models.video import Video, VideoStatus
from app.models.user import User

logger = logging.getLogger(__name__)


class VideoGenerationService:
    """Service for handling video generation with different AI models"""
    
    def __init__(self):
        self.output_dir = settings.upload_path / "videos"
        self.temp_dir = settings.upload_path / "temp"
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
    
    async def generate_with_stable_video_diffusion(
        self, 
        prompt: str, 
        negative_prompt: Optional[str] = None,
        input_image_path: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate video using Stable Video Diffusion"""
        try:
            # Import here to avoid loading heavy dependencies at startup
            import torch
            from diffusers import StableVideoDiffusionPipeline
            from PIL import Image
            import imageio
            
            logger.info("Loading Stable Video Diffusion model...")
            
            # Load the model
            pipe = StableVideoDiffusionPipeline.from_pretrained(
                "stabilityai/stable-video-diffusion-img2vid-xt",
                torch_dtype=torch.float16,
                variant="fp16"
            )
            
            # Move to GPU if available
            if torch.cuda.is_available():
                pipe = pipe.to("cuda")
                logger.info("Using GPU for video generation")
            else:
                logger.info("Using CPU for video generation")
            
            # Prepare input image
            if input_image_path and os.path.exists(input_image_path):
                image = Image.open(input_image_path).convert("RGB")
                image = image.resize((1024, 576))
            else:
                # Generate image from prompt first using DALL-E or Stable Diffusion
                image = await self._generate_image_from_prompt(prompt)
            
            # Generate video frames
            logger.info("Generating video frames...")
            frames = pipe(
                image=image,
                decode_chunk_size=8,
                generator=torch.manual_seed(kwargs.get('seed', 42)),
                motion_bucket_id=127,
                noise_aug_strength=0.02,
                num_frames=kwargs.get('num_frames', 25)
            ).frames[0]
            
            # Save video
            output_filename = f"{uuid.uuid4()}.mp4"
            output_path = self.output_dir / output_filename
            
            # Convert frames to video
            imageio.mimsave(
                str(output_path),
                frames,
                fps=kwargs.get('fps', 7),
                quality=8
            )
            
            logger.info(f"Video saved to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Stable Video Diffusion generation error: {e}")
            raise
    
    async def generate_with_runwayml(
        self, 
        prompt: str, 
        input_image_url: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate video using RunwayML API"""
        try:
            import httpx
            
            if not settings.RUNWAYML_API_KEY:
                raise ValueError("RunwayML API key not configured")
            
            logger.info("Generating video with RunwayML...")
            
            # Prepare request data
            request_data = {
                "text_prompt": prompt,
                "model": "gen3a_turbo",
                "aspect_ratio": "16:9",
                "duration": kwargs.get('duration_seconds', 5)
            }
            
            if input_image_url:
                request_data["image_prompt"] = input_image_url
            
            # Make API request
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.runwayml.com/v1/image_to_video",
                    headers={
                        "Authorization": f"Bearer {settings.RUNWAYML_API_KEY}",
                        "Content-Type": "application/json"
                    },
                    json=request_data,
                    timeout=300
                )
                
                if response.status_code != 200:
                    raise Exception(f"RunwayML API error: {response.text}")
                
                result = response.json()
                task_id = result.get("id")
                
                # Poll for completion
                video_url = await self._poll_runwayml_task(client, task_id)
                
                # Download video
                output_path = await self._download_video(client, video_url)
                
                logger.info(f"RunwayML video saved to: {output_path}")
                return output_path
                
        except Exception as e:
            logger.error(f"RunwayML generation error: {e}")
            raise
    
    async def _generate_image_from_prompt(self, prompt: str) -> "PIL.Image":
        """Generate image from text prompt using DALL-E or Stable Diffusion"""
        try:
            if settings.OPENAI_API_KEY:
                return await self._generate_with_dalle(prompt)
            else:
                return await self._generate_with_stable_diffusion(prompt)
        except Exception as e:
            logger.error(f"Image generation error: {e}")
            # Return a default image or raise
            raise
    
    async def _generate_with_dalle(self, prompt: str) -> "PIL.Image":
        """Generate image using DALL-E"""
        import openai
        from PIL import Image
        import httpx
        
        client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
        response = await client.images.generate(
            model="dall-e-3",
            prompt=prompt,
            size="1024x1024",
            quality="standard",
            n=1,
        )
        
        image_url = response.data[0].url
        
        # Download image
        async with httpx.AsyncClient() as http_client:
            img_response = await http_client.get(image_url)
            img_response.raise_for_status()
            
            temp_path = self.temp_dir / f"{uuid.uuid4()}.png"
            with open(temp_path, "wb") as f:
                f.write(img_response.content)
            
            return Image.open(temp_path).convert("RGB")
    
    async def _generate_with_stable_diffusion(self, prompt: str) -> "PIL.Image":
        """Generate image using Stable Diffusion"""
        import torch
        from diffusers import StableDiffusionPipeline
        
        pipe = StableDiffusionPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5",
            torch_dtype=torch.float16
        )
        
        if torch.cuda.is_available():
            pipe = pipe.to("cuda")
        
        image = pipe(prompt).images[0]
        return image
    
    async def _poll_runwayml_task(self, client: "httpx.AsyncClient", task_id: str) -> str:
        """Poll RunwayML task until completion"""
        import asyncio
        
        max_attempts = 60  # 5 minutes with 5-second intervals
        attempt = 0
        
        while attempt < max_attempts:
            response = await client.get(
                f"https://api.runwayml.com/v1/tasks/{task_id}",
                headers={"Authorization": f"Bearer {settings.RUNWAYML_API_KEY}"}
            )
            
            if response.status_code != 200:
                raise Exception(f"RunwayML polling error: {response.text}")
            
            result = response.json()
            status = result.get("status")
            
            if status == "SUCCEEDED":
                return result.get("output", {}).get("video_url")
            elif status == "FAILED":
                raise Exception(f"RunwayML generation failed: {result.get('error')}")
            
            await asyncio.sleep(5)
            attempt += 1
        
        raise Exception("RunwayML generation timeout")
    
    async def _download_video(self, client: "httpx.AsyncClient", video_url: str) -> str:
        """Download video from URL"""
        response = await client.get(video_url)
        response.raise_for_status()
        
        output_filename = f"{uuid.uuid4()}.mp4"
        output_path = self.output_dir / output_filename
        
        with open(output_path, "wb") as f:
            f.write(response.content)
        
        return str(output_path)
    
    def create_thumbnail(self, video_path: str) -> str:
        """Create thumbnail from video"""
        try:
            import cv2
            
            # Open video
            cap = cv2.VideoCapture(video_path)
            
            # Read first frame
            ret, frame = cap.read()
            if not ret:
                raise Exception("Could not read video frame")
            
            # Save thumbnail
            thumbnail_filename = f"{uuid.uuid4()}_thumb.jpg"
            thumbnail_path = self.output_dir / thumbnail_filename
            
            cv2.imwrite(str(thumbnail_path), frame)
            cap.release()
            
            return str(thumbnail_path)
            
        except Exception as e:
            logger.error(f"Thumbnail creation error: {e}")
            return ""


# Background task function
def generate_video_task(video_id: int):
    """Background task to generate video"""
    db = SessionLocal()
    service = VideoGenerationService()
    
    try:
        # Get video record
        video = db.query(Video).filter(Video.id == video_id).first()
        if not video:
            logger.error(f"Video not found: {video_id}")
            return
        
        # Update status
        video.status = VideoStatus.PROCESSING
        video.processing_started_at = datetime.utcnow()
        db.commit()
        
        logger.info(f"Starting video generation for video {video_id}")
        
        # Generate video based on model
        if video.model_used == "stable_video_diffusion":
            output_path = asyncio.run(service.generate_with_stable_video_diffusion(
                prompt=video.prompt_text,
                negative_prompt=video.negative_prompt,
                input_image_path=video.input_image_path,
                seed=video.seed,
                duration_seconds=video.duration_seconds
            ))
        elif video.model_used == "runwayml":
            output_path = asyncio.run(service.generate_with_runwayml(
                prompt=video.prompt_text,
                input_image_url=video.input_image_url,
                duration_seconds=video.duration_seconds
            ))
        else:
            raise ValueError(f"Unsupported model: {video.model_used}")
        
        # Create thumbnail
        thumbnail_path = service.create_thumbnail(output_path)
        
        # Get file size
        file_size_mb = os.path.getsize(output_path) / (1024 * 1024)
        
        # Update video record
        video.status = VideoStatus.COMPLETED
        video.output_video_path = output_path
        video.output_video_url = f"{settings.BACKEND_URL}/uploads/videos/{os.path.basename(output_path)}"
        video.thumbnail_path = thumbnail_path
        video.thumbnail_url = f"{settings.BACKEND_URL}/uploads/videos/{os.path.basename(thumbnail_path)}" if thumbnail_path else None
        video.file_size_mb = file_size_mb
        video.processing_completed_at = datetime.utcnow()
        video.processing_time_seconds = (video.processing_completed_at - video.processing_started_at).total_seconds()
        video.progress_percentage = 100
        
        # Update user stats
        user = db.query(User).filter(User.id == video.user_id).first()
        if user:
            user.videos_generated += 1
            user.storage_used_mb += file_size_mb
        
        db.commit()
        
        logger.info(f"Video generation completed for video {video_id}")
        
    except Exception as e:
        logger.error(f"Video generation failed for video {video_id}: {e}")
        
        # Update video status to failed
        video = db.query(Video).filter(Video.id == video_id).first()
        if video:
            video.status = VideoStatus.FAILED
            video.error_message = str(e)
            video.processing_completed_at = datetime.utcnow()
            if video.processing_started_at:
                video.processing_time_seconds = (video.processing_completed_at - video.processing_started_at).total_seconds()
            db.commit()
    
    finally:
        db.close()
