# Deployment Guide

This guide covers deploying the Video Generation Application to production environments.

## 🚀 Quick Deployment Options

### Option 1: Docker Compose (Recommended)

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd video-gen-app
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with production values
   ```

3. **Deploy with Docker Compose**:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Option 2: Cloud Deployment

Deploy individual components to cloud services:
- **Frontend**: Vercel, Netlify, or AWS S3 + CloudFront
- **Backend**: AWS ECS, Google Cloud Run, or DigitalOcean App Platform
- **Database**: AWS RDS, Google Cloud SQL, or managed PostgreSQL
- **Redis**: AWS ElastiCache, Google Cloud Memorystore, or Redis Cloud

## 🔧 Production Configuration

### Environment Variables

Create a production `.env` file with these essential variables:

```env
# Application
ENVIRONMENT=production
DEBUG=false
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://api.yourdomain.com

# Database
DATABASE_URL=********************************************/videogen_db
REDIS_URL=redis://your-redis-host:6379/0

# Security
SECRET_KEY=your-super-secure-secret-key-here
ALLOWED_ORIGINS=["https://yourdomain.com"]

# Google OAuth
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
GOOGLE_REDIRECT_URI=https://yourdomain.com/auth/callback

# AI APIs
OPENAI_API_KEY=your-openai-api-key
RUNWAYML_API_KEY=your-runwayml-api-key
STABILITY_API_KEY=your-stability-ai-api-key

# Cloud Storage (AWS S3)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=your-production-bucket
AWS_REGION=us-east-1

# Monitoring
SENTRY_DSN=your-sentry-dsn-for-error-tracking
LOG_LEVEL=INFO

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### Frontend Environment

Create `frontend/.env.production`:

```env
VITE_API_URL=https://api.yourdomain.com/api
VITE_GOOGLE_CLIENT_ID=your-production-google-client-id
VITE_NODE_ENV=production
```

## 🐳 Docker Production Setup

### Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  # Production PostgreSQL
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - app_network

  # Production Redis
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app_network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - app_network

  # Celery Worker
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    command: celery -A app.celery_app worker --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - app_network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    networks:
      - app_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    networks:
      - app_network

volumes:
  postgres_data:
  redis_data:

networks:
  app_network:
    driver: bridge
```

### Production Dockerfiles

**Backend Production Dockerfile** (`backend/Dockerfile.prod`):

```dockerfile
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN apt-get update && apt-get install -y \
    build-essential \
    ffmpeg \
    libmagic1 \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

RUN mkdir -p /app/uploads

EXPOSE 8000

CMD ["gunicorn", "app.main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

**Frontend Production Dockerfile** (`frontend/Dockerfile.prod`):

```dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.prod.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## ☁️ Cloud Deployment

### AWS Deployment

1. **ECS with Fargate**:
   - Create ECS cluster
   - Define task definitions for backend, frontend, and workers
   - Set up Application Load Balancer
   - Configure auto-scaling

2. **RDS for PostgreSQL**:
   ```bash
   aws rds create-db-instance \
     --db-instance-identifier videogen-db \
     --db-instance-class db.t3.micro \
     --engine postgres \
     --master-username videogen \
     --master-user-password your-password \
     --allocated-storage 20
   ```

3. **ElastiCache for Redis**:
   ```bash
   aws elasticache create-cache-cluster \
     --cache-cluster-id videogen-redis \
     --cache-node-type cache.t3.micro \
     --engine redis
   ```

4. **S3 for file storage**:
   ```bash
   aws s3 mb s3://your-videogen-bucket
   aws s3api put-bucket-cors --bucket your-videogen-bucket --cors-configuration file://cors.json
   ```

### Google Cloud Deployment

1. **Cloud Run for backend**:
   ```bash
   gcloud run deploy videogen-backend \
     --image gcr.io/your-project/videogen-backend \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

2. **Cloud SQL for PostgreSQL**:
   ```bash
   gcloud sql instances create videogen-db \
     --database-version=POSTGRES_13 \
     --tier=db-f1-micro \
     --region=us-central1
   ```

### DigitalOcean App Platform

Create `app.yaml`:

```yaml
name: videogen-app
services:
- name: backend
  source_dir: backend
  github:
    repo: your-username/videogen-app
    branch: main
  run_command: gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs
  
- name: frontend
  source_dir: frontend
  github:
    repo: your-username/videogen-app
    branch: main
  build_command: npm run build
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs

databases:
- name: videogen-db
  engine: PG
  version: "13"
  size: db-s-1vcpu-1gb
```

## 🔒 Security Considerations

### SSL/TLS Configuration

1. **Obtain SSL certificates**:
   ```bash
   # Using Let's Encrypt
   certbot --nginx -d yourdomain.com -d api.yourdomain.com
   ```

2. **Nginx SSL configuration**:
   ```nginx
   server {
       listen 443 ssl http2;
       server_name yourdomain.com;
       
       ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
       ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
       
       # Security headers
       add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-Content-Type-Options "nosniff" always;
   }
   ```

### Database Security

1. **Enable SSL connections**
2. **Use strong passwords**
3. **Restrict network access**
4. **Regular backups**

### API Security

1. **Rate limiting**
2. **Input validation**
3. **CORS configuration**
4. **API key rotation**

## 📊 Monitoring & Logging

### Application Monitoring

1. **Sentry for error tracking**:
   ```python
   import sentry_sdk
   sentry_sdk.init(dsn="your-sentry-dsn")
   ```

2. **Prometheus metrics**:
   ```python
   from prometheus_client import Counter, Histogram
   
   REQUEST_COUNT = Counter('requests_total', 'Total requests')
   REQUEST_LATENCY = Histogram('request_duration_seconds', 'Request latency')
   ```

### Infrastructure Monitoring

1. **Docker health checks**
2. **Database monitoring**
3. **Redis monitoring**
4. **Disk space monitoring**

### Log Management

1. **Centralized logging with ELK stack**
2. **Log rotation**
3. **Structured logging**

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Build and push Docker images
      run: |
        docker build -t videogen-backend ./backend
        docker build -t videogen-frontend ./frontend
        
    - name: Deploy to production
      run: |
        docker-compose -f docker-compose.prod.yml up -d
```

## 🚨 Backup & Recovery

### Database Backups

```bash
# Automated daily backups
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Restore from backup
psql $DATABASE_URL < backup_20240101.sql
```

### File Storage Backups

```bash
# S3 sync for file backups
aws s3 sync s3://your-bucket s3://your-backup-bucket
```

## 📈 Scaling Considerations

1. **Horizontal scaling**: Multiple backend instances
2. **Database read replicas**: For read-heavy workloads
3. **CDN**: For static assets and videos
4. **Load balancing**: Distribute traffic across instances
5. **Caching**: Redis for frequently accessed data

## 🔧 Maintenance

### Regular Tasks

1. **Update dependencies**
2. **Security patches**
3. **Database maintenance**
4. **Log cleanup**
5. **Certificate renewal**

### Health Checks

1. **Application health endpoints**
2. **Database connectivity**
3. **External service availability**
4. **Disk space monitoring**
