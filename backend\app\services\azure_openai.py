import httpx
import logging
from typing import Optional, Dict, Any
import base64
import uuid
from pathlib import Path

from app.core.config import settings
from app.utils.file_manager import file_manager

logger = logging.getLogger(__name__)


class AzureOpenAIService:
    """Service for Azure OpenAI DALL-E 3 image generation"""
    
    def __init__(self):
        self.api_key = settings.AZURE_OPENAI_API_KEY
        self.endpoint = settings.AZURE_OPENAI_ENDPOINT
        self.api_version = settings.AZURE_OPENAI_API_VERSION
        self.deployment_name = settings.AZURE_DALLE_DEPLOYMENT_NAME
        
        if not self.api_key or not self.endpoint:
            logger.warning("Azure OpenAI credentials not configured")
    
    async def generate_image(
        self,
        prompt: str,
        size: str = "1024x1024",
        style: str = "vivid",
        quality: str = "standard",
        response_format: str = "url"
    ) -> Dict[str, Any]:
        """
        Generate image using Azure OpenAI DALL-E 3
        
        Args:
            prompt: Text description of the image to generate
            size: Image size (1024x1024, 1024x1792, 1792x1024)
            style: Image style (vivid, natural)
            quality: Image quality (standard, hd)
            response_format: Response format (url, b64_json)
        
        Returns:
            Dictionary with image URL and metadata
        """
        if not self.api_key or not self.endpoint:
            raise ValueError("Azure OpenAI credentials not configured")
        
        try:
            url = f"{self.endpoint}/openai/deployments/{self.deployment_name}/images/generations"
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            params = {
                "api-version": self.api_version
            }
            
            data = {
                "model": "dall-e-3",
                "prompt": prompt,
                "size": size,
                "style": style,
                "quality": quality,
                "n": 1,
                "response_format": response_format
            }
            
            logger.info(f"Generating image with Azure DALL-E 3: {prompt[:50]}...")
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(url, headers=headers, params=params, json=data)
                
                if response.status_code != 200:
                    error_detail = response.text
                    logger.error(f"Azure OpenAI API error: {response.status_code} - {error_detail}")
                    raise Exception(f"Azure OpenAI API error: {response.status_code} - {error_detail}")
                
                result = response.json()
                
                if "data" not in result or not result["data"]:
                    raise Exception("No image data returned from Azure OpenAI")
                
                image_data = result["data"][0]
                
                # Handle different response formats
                if response_format == "url":
                    image_url = image_data.get("url")
                    if not image_url:
                        raise Exception("No image URL returned from Azure OpenAI")
                    
                    # Download and save the image
                    saved_image = await self._download_and_save_image(image_url, prompt)
                    
                    return {
                        "url": saved_image["file_url"],
                        "local_path": saved_image["file_path"],
                        "revised_prompt": image_data.get("revised_prompt", prompt),
                        "size": size,
                        "style": style,
                        "quality": quality,
                        "model": "dall-e-3",
                        "provider": "azure_openai"
                    }
                
                elif response_format == "b64_json":
                    b64_data = image_data.get("b64_json")
                    if not b64_data:
                        raise Exception("No base64 image data returned from Azure OpenAI")
                    
                    # Save base64 image
                    saved_image = await self._save_base64_image(b64_data, prompt)
                    
                    return {
                        "url": saved_image["file_url"],
                        "local_path": saved_image["file_path"],
                        "revised_prompt": image_data.get("revised_prompt", prompt),
                        "size": size,
                        "style": style,
                        "quality": quality,
                        "model": "dall-e-3",
                        "provider": "azure_openai"
                    }
                
        except httpx.TimeoutException:
            logger.error("Azure OpenAI request timeout")
            raise Exception("Image generation request timed out")
        except Exception as e:
            logger.error(f"Azure OpenAI image generation error: {e}")
            raise
    
    async def _download_and_save_image(self, image_url: str, prompt: str) -> Dict[str, str]:
        """Download image from URL and save it"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url)
                response.raise_for_status()
                
                image_content = response.content
                
                # Generate filename
                filename = f"dalle3_{uuid.uuid4().hex[:8]}.png"
                
                # Save image using file manager (will use Azure Blob Storage)
                file_path, file_url = await file_manager.save_file(
                    image_content,
                    filename,
                    "images"
                )
                
                logger.info(f"Image saved: {file_path}")
                
                return {
                    "file_path": file_path,
                    "file_url": file_url,
                    "filename": filename
                }
                
        except Exception as e:
            logger.error(f"Error downloading and saving image: {e}")
            raise
    
    async def _save_base64_image(self, b64_data: str, prompt: str) -> Dict[str, str]:
        """Save base64 encoded image"""
        try:
            # Decode base64 data
            image_content = base64.b64decode(b64_data)
            
            # Generate filename
            filename = f"dalle3_{uuid.uuid4().hex[:8]}.png"
            
            # Save image
            file_path, file_url = await file_manager.save_file(
                image_content, 
                filename, 
                "images"
            )
            
            logger.info(f"Base64 image saved: {file_path}")
            
            return {
                "file_path": file_path,
                "file_url": file_url,
                "filename": filename
            }
            
        except Exception as e:
            logger.error(f"Error saving base64 image: {e}")
            raise
    
    async def generate_image_for_video(
        self,
        prompt: str,
        video_aspect_ratio: str = "16:9"
    ) -> Dict[str, Any]:
        """
        Generate image optimized for video generation
        
        Args:
            prompt: Text description
            video_aspect_ratio: Target video aspect ratio (16:9, 1:1, 9:16)
        
        Returns:
            Dictionary with image data optimized for video
        """
        # Map video aspect ratios to DALL-E 3 sizes
        size_mapping = {
            "16:9": "1792x1024",  # Landscape
            "1:1": "1024x1024",   # Square
            "9:16": "1024x1792"   # Portrait
        }
        
        size = size_mapping.get(video_aspect_ratio, "1024x1024")
        
        # Enhance prompt for video generation
        enhanced_prompt = f"{prompt}. High quality, detailed, suitable for video generation, cinematic composition"
        
        return await self.generate_image(
            prompt=enhanced_prompt,
            size=size,
            style="vivid",
            quality="hd"
        )
    
    def is_configured(self) -> bool:
        """Check if Azure OpenAI is properly configured"""
        return bool(self.api_key and self.endpoint)


# Global instance
azure_openai_service = AzureOpenAIService()
