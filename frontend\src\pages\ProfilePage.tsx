import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Crown, User, Settings } from 'lucide-react';

const ProfilePage: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center space-x-4 mb-6">
          <img
            className="h-16 w-16 rounded-full"
            src={user?.avatar_url || `https://ui-avatars.com/api/?name=${user?.full_name || user?.email}&background=3b82f6&color=fff`}
            alt={user?.full_name || user?.email}
          />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {user?.full_name || user?.email}
            </h1>
            <div className="flex items-center space-x-2">
              {user?.is_premium && (
                <div className="flex items-center space-x-1 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm">
                  <Crown className="h-3 w-3" />
                  <span>Premium</span>
                </div>
              )}
              <span className="text-gray-500 capitalize">{user?.subscription_type}</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{user?.videos_generated || 0}</div>
            <div className="text-sm text-gray-600">Videos Generated</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{user?.storage_used_mb?.toFixed(1) || 0} MB</div>
            <div className="text-sm text-gray-600">Storage Used</div>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-900">{user?.api_calls_count || 0}</div>
            <div className="text-sm text-gray-600">API Calls Used</div>
          </div>
        </div>
      </div>

      <div className="text-center py-12">
        <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Profile Settings</h3>
        <p className="text-gray-500">Advanced profile management coming soon</p>
      </div>
    </div>
  );
};

export default ProfilePage;
