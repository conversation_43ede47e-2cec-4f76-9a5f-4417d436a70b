import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import <PERSON><PERSON><PERSON>

from app.core.security import create_access_token, verify_token


class TestAuth:
    """Test authentication endpoints"""
    
    def test_health_endpoint(self, client: TestClient):
        """Test health endpoint is accessible without auth"""
        response = client.get("/health")
        assert response.status_code == 200
        assert "status" in response.json()
    
    def test_auth_config_endpoint(self, client: TestClient):
        """Test auth config endpoint"""
        response = client.get("/api/auth/config")
        assert response.status_code == 200
        data = response.json()
        assert "google_client_id" in data
    
    @patch('app.api.auth.verify_google_token')
    def test_google_auth_success(self, mock_verify_google, client: TestClient, db_session):
        """Test successful Google authentication"""
        # Mock Google token verification
        mock_verify_google.return_value = {
            "email": "<EMAIL>",
            "sub": "google_user_id",
            "name": "New User",
            "picture": "https://example.com/avatar.jpg"
        }
        
        response = client.post("/api/auth/google", json={
            "token": "mock_google_token"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert "user" in data
        assert data["user"]["email"] == "<EMAIL>"
    
    @patch('app.api.auth.verify_google_token')
    def test_google_auth_invalid_token(self, mock_verify_google, client: TestClient):
        """Test Google authentication with invalid token"""
        mock_verify_google.return_value = None
        
        response = client.post("/api/auth/google", json={
            "token": "invalid_token"
        })
        
        assert response.status_code == 401
        assert "Invalid Google token" in response.json()["detail"]
    
    def test_refresh_token_success(self, client: TestClient, test_user):
        """Test successful token refresh"""
        from app.core.security import create_refresh_token
        
        token_data = {"sub": str(test_user.id), "email": test_user.email}
        refresh_token = create_refresh_token(token_data)
        
        response = client.post("/api/auth/refresh", json={
            "refresh_token": refresh_token
        })
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
    
    def test_refresh_token_invalid(self, client: TestClient):
        """Test token refresh with invalid token"""
        response = client.post("/api/auth/refresh", json={
            "refresh_token": "invalid_refresh_token"
        })
        
        assert response.status_code == 401
        assert "Invalid refresh token" in response.json()["detail"]
    
    def test_get_current_user_success(self, client: TestClient, auth_headers):
        """Test getting current user with valid token"""
        response = client.get("/api/auth/me", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "email" in data
        assert "id" in data
    
    def test_get_current_user_no_token(self, client: TestClient):
        """Test getting current user without token"""
        response = client.get("/api/auth/me")
        
        assert response.status_code == 401
    
    def test_get_current_user_invalid_token(self, client: TestClient):
        """Test getting current user with invalid token"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/auth/me", headers=headers)
        
        assert response.status_code == 401
    
    def test_logout_success(self, client: TestClient, auth_headers):
        """Test successful logout"""
        response = client.post("/api/auth/logout", headers=auth_headers)
        
        assert response.status_code == 200
        assert "Successfully logged out" in response.json()["message"]


class TestTokens:
    """Test token creation and verification"""
    
    def test_create_access_token(self):
        """Test access token creation"""
        data = {"sub": "123", "email": "<EMAIL>"}
        token = create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
    
    def test_verify_valid_token(self):
        """Test verifying valid token"""
        data = {"sub": "123", "email": "<EMAIL>"}
        token = create_access_token(data)
        
        payload = verify_token(token)
        
        assert payload is not None
        assert payload["sub"] == "123"
        assert payload["email"] == "<EMAIL>"
        assert payload["type"] == "access"
    
    def test_verify_invalid_token(self):
        """Test verifying invalid token"""
        payload = verify_token("invalid_token")
        
        assert payload is None
    
    def test_verify_expired_token(self):
        """Test verifying expired token"""
        from datetime import datetime, timedelta
        from jose import jwt
        from app.core.config import settings
        
        # Create expired token
        expired_data = {
            "sub": "123",
            "email": "<EMAIL>",
            "exp": datetime.utcnow() - timedelta(minutes=1),
            "type": "access"
        }
        expired_token = jwt.encode(expired_data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        payload = verify_token(expired_token)
        
        assert payload is None
