from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging

from app.core.security import get_current_user_token
from app.services.azure_openai import azure_openai_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/azure-dalle", tags=["Azure DALL-E"])


class ImageGenerationRequest(BaseModel):
    prompt: str
    size: Optional[str] = "1024x1024"
    style: Optional[str] = "vivid"
    quality: Optional[str] = "standard"
    video_optimized: Optional[bool] = False
    video_aspect_ratio: Optional[str] = "16:9"


class ImageGenerationResponse(BaseModel):
    url: str
    local_path: str
    revised_prompt: str
    size: str
    style: str
    quality: str
    model: str
    provider: str


@router.post("/generate-image", response_model=ImageGenerationResponse)
async def generate_image(
    request: ImageGenerationRequest,
    current_user: dict = Depends(get_current_user_token)
):
    """Generate image using Azure OpenAI DALL-E 3"""
    
    if not azure_openai_service.is_configured():
        raise HTTPException(
            status_code=503,
            detail="Azure OpenAI service is not configured. Please check your API credentials."
        )
    
    try:
        logger.info(f"User {current_user['user_id']} generating image: {request.prompt[:50]}...")
        
        if request.video_optimized:
            # Generate image optimized for video
            result = await azure_openai_service.generate_image_for_video(
                prompt=request.prompt,
                video_aspect_ratio=request.video_aspect_ratio
            )
        else:
            # Generate regular image
            result = await azure_openai_service.generate_image(
                prompt=request.prompt,
                size=request.size,
                style=request.style,
                quality=request.quality
            )
        
        logger.info(f"Image generated successfully for user {current_user['user_id']}")
        
        return ImageGenerationResponse(**result)
        
    except Exception as e:
        logger.error(f"Image generation failed for user {current_user['user_id']}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Image generation failed: {str(e)}"
        )


@router.get("/status")
async def get_azure_dalle_status():
    """Get Azure DALL-E service status"""
    
    is_configured = azure_openai_service.is_configured()
    
    return {
        "service": "Azure OpenAI DALL-E 3",
        "configured": is_configured,
        "endpoint": azure_openai_service.endpoint if is_configured else None,
        "deployment": azure_openai_service.deployment_name if is_configured else None,
        "api_version": azure_openai_service.api_version if is_configured else None
    }


@router.get("/test")
async def test_azure_dalle(
    current_user: dict = Depends(get_current_user_token)
):
    """Test Azure DALL-E 3 with a simple prompt"""
    
    if not azure_openai_service.is_configured():
        raise HTTPException(
            status_code=503,
            detail="Azure OpenAI service is not configured"
        )
    
    try:
        test_prompt = "A beautiful red fox in an autumn forest, photorealistic, high quality"
        
        result = await azure_openai_service.generate_image(
            prompt=test_prompt,
            size="1024x1024",
            style="vivid",
            quality="standard"
        )
        
        return {
            "message": "Azure DALL-E 3 test successful",
            "test_prompt": test_prompt,
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Azure DALL-E 3 test failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Test failed: {str(e)}"
        )
