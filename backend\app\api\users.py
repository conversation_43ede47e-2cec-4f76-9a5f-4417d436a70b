from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import Optional, List
import logging

from app.core.database import get_db
from app.core.security import get_current_user_token
from app.models.user import User

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models
class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    preferences: Optional[dict] = None


class UserResponse(BaseModel):
    id: int
    email: str
    full_name: Optional[str]
    avatar_url: Optional[str]
    is_premium: bool
    subscription_type: str
    videos_generated: int
    storage_used_mb: int
    created_at: str
    
    class Config:
        from_attributes = True


class UserStats(BaseModel):
    total_videos: int
    completed_videos: int
    processing_videos: int
    failed_videos: int
    total_storage_mb: float
    api_calls_used: int
    api_calls_remaining: int


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Get current user's profile"""
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user


@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    user_update: UserUpdate,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Update current user's profile"""
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update fields
    if user_update.full_name is not None:
        user.full_name = user_update.full_name
    
    if user_update.preferences is not None:
        user.preferences = user_update.preferences
    
    db.commit()
    db.refresh(user)
    
    logger.info(f"User profile updated: {user.email}")
    return user


@router.get("/stats", response_model=UserStats)
async def get_user_stats(
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Get current user's statistics"""
    from app.models.video import Video, VideoStatus
    
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Get video statistics
    total_videos = db.query(Video).filter(Video.user_id == user.id).count()
    completed_videos = db.query(Video).filter(
        Video.user_id == user.id,
        Video.status == VideoStatus.COMPLETED
    ).count()
    processing_videos = db.query(Video).filter(
        Video.user_id == user.id,
        Video.status.in_([VideoStatus.PENDING, VideoStatus.PROCESSING])
    ).count()
    failed_videos = db.query(Video).filter(
        Video.user_id == user.id,
        Video.status == VideoStatus.FAILED
    ).count()
    
    # Calculate total storage used
    from sqlalchemy import func
    total_storage = db.query(func.sum(Video.file_size_mb)).filter(
        Video.user_id == user.id,
        Video.status == VideoStatus.COMPLETED
    ).scalar() or 0.0
    
    return UserStats(
        total_videos=total_videos,
        completed_videos=completed_videos,
        processing_videos=processing_videos,
        failed_videos=failed_videos,
        total_storage_mb=total_storage,
        api_calls_used=user.api_calls_count,
        api_calls_remaining=user.remaining_api_calls
    )


@router.delete("/account")
async def delete_user_account(
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Delete current user's account"""
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Soft delete - deactivate account
    user.is_active = False
    user.email = f"deleted_{user.id}_{user.email}"
    db.commit()
    
    logger.info(f"User account deleted: {current_user['email']}")
    return {"message": "Account successfully deleted"}


@router.post("/upgrade-premium")
async def upgrade_to_premium(
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Upgrade user to premium (placeholder for payment integration)"""
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if user.is_premium:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is already premium"
        )
    
    # TODO: Integrate with payment processor (Stripe, PayPal, etc.)
    # For now, just upgrade the user
    user.is_premium = True
    user.subscription_type = "premium"
    user.api_calls_limit = 1000  # Increase API limit
    
    from datetime import datetime, timedelta
    user.subscription_expires_at = datetime.utcnow() + timedelta(days=30)
    
    db.commit()
    
    logger.info(f"User upgraded to premium: {user.email}")
    return {"message": "Successfully upgraded to premium", "expires_at": user.subscription_expires_at}
