# Video Generation API Documentation

## Overview

The Video Generation API provides endpoints for creating AI-generated videos from text prompts and images. It supports multiple AI models, user authentication, file management, and comprehensive video processing capabilities.

**Base URL**: `http://localhost:8000/api`

## Authentication

All API endpoints (except authentication endpoints) require a valid JWT token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

### Get Authentication Token

**POST** `/auth/google`

Authenticate using Google OAuth token.

```json
{
  "token": "google_oauth_token"
}
```

**Response:**
```json
{
  "access_token": "jwt_access_token",
  "refresh_token": "jwt_refresh_token",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "is_premium": false,
    "subscription_type": "free"
  }
}
```

### Refresh Token

**POST** `/auth/refresh`

Refresh an expired access token.

```json
{
  "refresh_token": "your_refresh_token"
}
```

## Video Generation

### Generate Video

**POST** `/videos/generate`

Create a new video generation request.

```json
{
  "title": "My Amazing Video",
  "prompt_text": "A beautiful sunset over the ocean with waves gently crashing on the shore",
  "negative_prompt": "blurry, low quality, distorted",
  "input_image_url": "https://example.com/image.jpg",
  "model": "stable_video_diffusion",
  "resolution": "1024x576",
  "duration_seconds": 4.0,
  "guidance_scale": 7.5,
  "num_inference_steps": 25,
  "seed": 42
}
```

**Response:**
```json
{
  "id": 123,
  "title": "My Amazing Video",
  "status": "pending",
  "progress_percentage": 0,
  "created_at": "2024-01-01T12:00:00Z",
  "estimated_processing_time": 120
}
```

### List Videos

**GET** `/videos/`

Get a paginated list of user's videos.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `per_page` (int): Items per page (default: 20)
- `status_filter` (string): Filter by status (pending, processing, completed, failed)

**Response:**
```json
{
  "videos": [
    {
      "id": 123,
      "title": "My Amazing Video",
      "status": "completed",
      "progress_percentage": 100,
      "output_video_url": "https://example.com/video.mp4",
      "thumbnail_url": "https://example.com/thumbnail.jpg",
      "duration_seconds": 4.0,
      "file_size_mb": 15.2,
      "created_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "per_page": 20
}
```

### Get Video Details

**GET** `/videos/{video_id}`

Get detailed information about a specific video.

**Response:**
```json
{
  "id": 123,
  "title": "My Amazing Video",
  "description": "A beautiful video description",
  "prompt_text": "A beautiful sunset over the ocean",
  "status": "completed",
  "progress_percentage": 100,
  "output_video_url": "https://example.com/video.mp4",
  "thumbnail_url": "https://example.com/thumbnail.jpg",
  "model_used": "stable_video_diffusion",
  "resolution": "1024x576",
  "duration_seconds": 4.0,
  "file_size_mb": 15.2,
  "processing_time_seconds": 120,
  "view_count": 5,
  "download_count": 2,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Download Video

**POST** `/videos/{video_id}/download`

Get download URL for a completed video.

**Response:**
```json
{
  "download_url": "https://example.com/video.mp4",
  "filename": "my_amazing_video.mp4",
  "file_size_mb": 15.2
}
```

### Delete Video

**DELETE** `/videos/{video_id}`

Delete a video and its associated files.

**Response:**
```json
{
  "message": "Video deleted successfully"
}
```

## File Upload

### Upload Image

**POST** `/upload/image`

Upload an image file for video generation.

**Form Data:**
- `file`: Image file (JPG, PNG, GIF, WebP)

**Response:**
```json
{
  "message": "Image uploaded successfully",
  "filename": "original_filename.jpg",
  "file_path": "/uploads/images/unique_id.jpg",
  "file_url": "http://localhost:8000/uploads/images/unique_id.jpg",
  "file_size_mb": 2.5
}
```

### Get Upload Limits

**GET** `/upload/limits`

Get current user's upload limits and usage.

**Response:**
```json
{
  "max_file_size_mb": 100,
  "storage_limit_mb": 1024,
  "storage_used_mb": 250.5,
  "storage_remaining_mb": 773.5,
  "allowed_image_extensions": ["jpg", "jpeg", "png", "gif", "webp"],
  "allowed_video_extensions": ["mp4", "avi", "mov", "webm"]
}
```

## Prompts

### Create Prompt

**POST** `/prompts/`

Save a prompt for reuse.

```json
{
  "title": "Ocean Sunset",
  "prompt_text": "A beautiful sunset over the ocean with waves",
  "negative_prompt": "blurry, low quality",
  "category": "nature",
  "style": "cinematic",
  "mood": "peaceful",
  "tags": ["sunset", "ocean", "peaceful"],
  "is_public": false
}
```

### List Prompts

**GET** `/prompts/`

Get user's prompts with optional filtering.

**Query Parameters:**
- `page` (int): Page number
- `per_page` (int): Items per page
- `category` (string): Filter by category
- `style` (string): Filter by style
- `public_only` (bool): Show only public prompts

### Get Popular Prompts

**GET** `/prompts/popular`

Get popular public prompts.

**Query Parameters:**
- `limit` (int): Number of prompts to return (default: 10)

## User Management

### Get User Profile

**GET** `/users/profile`

Get current user's profile information.

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "avatar_url": "https://example.com/avatar.jpg",
  "is_premium": false,
  "subscription_type": "free",
  "videos_generated": 5,
  "storage_used_mb": 250.5,
  "created_at": "2024-01-01T12:00:00Z"
}
```

### Update User Profile

**PUT** `/users/profile`

Update user profile information.

```json
{
  "full_name": "John Smith",
  "preferences": {
    "theme": "dark",
    "default_model": "stable_video_diffusion"
  }
}
```

### Get User Statistics

**GET** `/users/stats`

Get user's usage statistics.

**Response:**
```json
{
  "total_videos": 10,
  "completed_videos": 8,
  "processing_videos": 1,
  "failed_videos": 1,
  "total_storage_mb": 250.5,
  "api_calls_used": 45,
  "api_calls_remaining": 55
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "detail": "Error message description"
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid or missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `413` - Payload Too Large (file size exceeded)
- `422` - Unprocessable Entity (validation error)
- `429` - Too Many Requests (rate limit exceeded)
- `500` - Internal Server Error

## Rate Limits

- **Free Users**: 100 API calls per month
- **Premium Users**: 1000 API calls per month
- **Rate Limiting**: 60 requests per minute per IP

## Supported AI Models

### Stable Video Diffusion (Free)
- **Model ID**: `stable_video_diffusion`
- **Description**: Open-source video generation model
- **Max Duration**: 10 seconds
- **Resolutions**: 1024x576, 768x768, 576x1024

### RunwayML (Premium)
- **Model ID**: `runwayml`
- **Description**: Commercial video generation service
- **Max Duration**: 30 seconds
- **Resolutions**: Multiple aspect ratios supported
- **Requirements**: Premium subscription

## WebSocket Events (Real-time Updates)

Connect to `/ws/videos/{video_id}` for real-time video generation progress:

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/videos/123');

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Progress:', data.progress_percentage);
  console.log('Status:', data.status);
};
```

## SDKs and Examples

### Python SDK Example

```python
import requests

# Authenticate
auth_response = requests.post('http://localhost:8000/api/auth/google', {
    'token': 'your_google_token'
})
token = auth_response.json()['access_token']

# Generate video
headers = {'Authorization': f'Bearer {token}'}
video_data = {
    'title': 'My Video',
    'prompt_text': 'A beautiful landscape',
    'model': 'stable_video_diffusion'
}

response = requests.post(
    'http://localhost:8000/api/videos/generate',
    json=video_data,
    headers=headers
)
video = response.json()
print(f"Video ID: {video['id']}")
```

### JavaScript SDK Example

```javascript
// Authenticate with Google
const authResponse = await fetch('/api/auth/google', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ token: googleToken })
});
const { access_token } = await authResponse.json();

// Generate video
const videoResponse = await fetch('/api/videos/generate', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    title: 'My Video',
    prompt_text: 'A beautiful landscape',
    model: 'stable_video_diffusion'
  })
});
const video = await videoResponse.json();
console.log('Video ID:', video.id);
```
