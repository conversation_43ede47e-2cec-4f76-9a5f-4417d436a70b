import React from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService } from '@/services/api';
import LoadingSpinner from '@/components/LoadingSpinner';
import { VideoStatus } from '@/types';
import toast from 'react-hot-toast';
import { 
  ArrowLeft,
  Download,
  Eye,
  Clock,
  Calendar,
  Settings,
  Trash2,
  Share2,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  RotateCcw
} from 'lucide-react';

const VideoDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { data: video, isLoading, error } = useQuery({
    queryKey: ['video', id],
    queryFn: () => apiService.getVideo(Number(id)),
    enabled: !!id,
    refetchInterval: (data) => {
      // Refetch every 5 seconds if video is processing
      return data?.status === VideoStatus.PROCESSING ? 5000 : false;
    },
  });

  const deleteVideoMutation = useMutation({
    mutationFn: () => apiService.deleteVideo(Number(id)),
    onSuccess: () => {
      toast.success('Video deleted successfully');
      navigate('/videos');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete video');
    },
  });

  const downloadVideoMutation = useMutation({
    mutationFn: () => apiService.downloadVideo(Number(id)),
    onSuccess: (data) => {
      // Create download link
      const link = document.createElement('a');
      link.href = data.download_url;
      link.download = data.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Download started');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to download video');
    },
  });

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
      deleteVideoMutation.mutate();
    }
  };

  const handleDownload = () => {
    downloadVideoMutation.mutate();
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: video?.title,
          text: video?.description || video?.prompt_text,
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case VideoStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case VideoStatus.PROCESSING:
        return 'bg-blue-100 text-blue-800';
      case VideoStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case VideoStatus.FAILED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading video..." />
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">
          <Clock className="h-12 w-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Video not found</h3>
        <p className="text-gray-500 mb-4">The video you're looking for doesn't exist or has been deleted.</p>
        <Link to="/videos" className="btn-primary">
          Back to Videos
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => navigate('/videos')}
          className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Videos</span>
        </button>
        <div className="flex items-center space-x-2">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(video.status)}`}>
            {video.status}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Player */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="aspect-video bg-gray-900 relative">
              {video.status === VideoStatus.COMPLETED && video.output_video_url ? (
                <video
                  controls
                  className="w-full h-full"
                  poster={video.thumbnail_url}
                >
                  <source src={video.output_video_url} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              ) : video.status === VideoStatus.PROCESSING ? (
                <div className="w-full h-full flex flex-col items-center justify-center text-white">
                  <LoadingSpinner size="lg" />
                  <p className="mt-4 text-lg font-medium">Generating Video...</p>
                  <p className="text-sm text-gray-300 mt-2">
                    Progress: {video.progress_percentage}%
                  </p>
                  <div className="w-64 bg-gray-700 rounded-full h-2 mt-4">
                    <div 
                      className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${video.progress_percentage}%` }}
                    />
                  </div>
                  <p className="text-xs text-gray-400 mt-4">
                    Estimated time remaining: {Math.max(0, video.estimated_processing_time - (video.processing_time_seconds || 0))} seconds
                  </p>
                </div>
              ) : video.status === VideoStatus.FAILED ? (
                <div className="w-full h-full flex flex-col items-center justify-center text-white">
                  <div className="text-red-400 mb-4">
                    <Clock className="h-12 w-12" />
                  </div>
                  <p className="text-lg font-medium">Generation Failed</p>
                  <p className="text-sm text-gray-300 mt-2 text-center max-w-md">
                    {video.error_message || 'An error occurred during video generation'}
                  </p>
                  <Link
                    to="/generate"
                    className="mt-4 btn-primary"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Try Again
                  </Link>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center text-white">
                  <div className="text-center">
                    <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-lg font-medium">Waiting to Process</p>
                    <p className="text-sm text-gray-300">Your video is in the queue</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* Video Info */}
            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {video.title}
              </h1>
              {video.description && (
                <p className="text-gray-600 mb-4">
                  {video.description}
                </p>
              )}
              
              {/* Actions */}
              <div className="flex items-center space-x-4">
                {video.status === VideoStatus.COMPLETED && (
                  <button
                    onClick={handleDownload}
                    disabled={downloadVideoMutation.isPending}
                    className="btn-primary flex items-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>
                      {downloadVideoMutation.isPending ? 'Preparing...' : 'Download'}
                    </span>
                  </button>
                )}
                <button
                  onClick={handleShare}
                  className="btn-outline flex items-center space-x-2"
                >
                  <Share2 className="h-4 w-4" />
                  <span>Share</span>
                </button>
                <button
                  onClick={handleDelete}
                  disabled={deleteVideoMutation.isPending}
                  className="btn-outline text-red-600 border-red-300 hover:bg-red-50 flex items-center space-x-2"
                >
                  <Trash2 className="h-4 w-4" />
                  <span>Delete</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Video Details */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Video Details</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(video.status)}`}>
                  {video.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Model:</span>
                <span className="font-medium">{video.model_used}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Resolution:</span>
                <span className="font-medium">{video.resolution}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Duration:</span>
                <span className="font-medium">{video.duration_seconds}s</span>
              </div>
              {video.file_size_mb && (
                <div className="flex justify-between">
                  <span className="text-gray-600">File Size:</span>
                  <span className="font-medium">{video.file_size_mb.toFixed(1)} MB</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Created:</span>
                <span className="font-medium">
                  {new Date(video.created_at).toLocaleDateString()}
                </span>
              </div>
              {video.processing_time_seconds && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Processing Time:</span>
                  <span className="font-medium">
                    {Math.round(video.processing_time_seconds)}s
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Stats */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Eye className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Views</span>
                </div>
                <span className="font-medium">{video.view_count}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Download className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Downloads</span>
                </div>
                <span className="font-medium">{video.download_count}</span>
              </div>
            </div>
          </div>

          {/* Prompt */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Prompt</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Text Prompt
                </label>
                <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                  {video.prompt_text}
                </p>
              </div>
              {video.negative_prompt && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Negative Prompt
                  </label>
                  <p className="text-sm text-gray-900 bg-gray-50 p-3 rounded-md">
                    {video.negative_prompt}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoDetailPage;
