import numpy as np
import torch
import librosa
import random
from transformers import FeatureExtractionMixin, BatchFeature, Wav2Vec2Processor, Wav2Vec2Model
from typing import List, Optional, Union, Dict, Any
import logging

logger = logging.getLogger(__name__)


class EmotionAVFeatureExtractor(FeatureExtractionMixin):
    """
    Feature extractor for emotion classification that combines:
    - Wav2Vec2 features
    - MFCC features  
    - Prosodic features (pitch, energy, etc.)
    """
    
    model_input_names = ["input_features"]
    
    def __init__(
        self,
        sampling_rate: int = 16000,
        feature_size: int = 787,  # wav2vec2 (768) + mfcc (13) + prosodic (6)
        n_mfcc: int = 13,
        augment: bool = False,
        wav2vec2_model_name: str = "facebook/wav2vec2-base-960h",
        **kwargs
    ):
        super().__init__(**kwargs)
        
        self.sampling_rate = sampling_rate
        self.feature_size = feature_size
        self.n_mfcc = n_mfcc
        self.augment = augment
        self.wav2vec2_model_name = wav2vec2_model_name
        
        # Store lazy references instead of loading immediately
        self._wav2vec2_processor = None
        self._wav2vec2_model = None
    
    @property
    def wav2vec2_processor(self):
        """Lazy loading of Wav2Vec2 processor."""
        if self._wav2vec2_processor is None:
            self._wav2vec2_processor = Wav2Vec2Processor.from_pretrained(self.wav2vec2_model_name)
        return self._wav2vec2_processor
    
    @property
    def wav2vec2_model(self):
        """Lazy loading of Wav2Vec2 model."""
        if self._wav2vec2_model is None:
            self._wav2vec2_model = Wav2Vec2Model.from_pretrained(self.wav2vec2_model_name)
            
            # Freeze Wav2Vec2 model except last 2 transformer layers (as in your training)
            for param in self._wav2vec2_model.parameters():
                param.requires_grad = False
            for layer in self._wav2vec2_model.encoder.layers[-2:]:
                for param in layer.parameters():
                    param.requires_grad = True
                    
        return self._wav2vec2_model
    
    def augment_audio(self, y: np.ndarray, sr: int) -> np.ndarray:
        """Apply audio augmentation techniques."""
        if not self.augment:
            return y
            
        if random.random() < 0.5:
            y = librosa.effects.pitch_shift(y=y, sr=sr, n_steps=random.uniform(-2, 2))
        if random.random() < 0.5:
            y = librosa.effects.time_stretch(y=y, rate=random.uniform(0.9, 1.1))
        return y
    
    def extract_wav2vec2_features(self, audio: np.ndarray) -> np.ndarray:
        """Extract Wav2Vec2 features from audio."""
        inputs = self.wav2vec2_processor(
            audio, 
            return_tensors="pt", 
            sampling_rate=self.sampling_rate
        )
        
        with torch.no_grad():
            wav_feat = self.wav2vec2_model(**inputs).last_hidden_state.mean(dim=1).squeeze().numpy()
        
        return wav_feat
    
    def extract_mfcc_features(self, audio: np.ndarray) -> np.ndarray:
        """Extract MFCC features from audio."""
        mfcc = librosa.feature.mfcc(
            y=audio, 
            sr=self.sampling_rate, 
            n_mfcc=self.n_mfcc
        ).mean(axis=1)
        return mfcc
    
    def extract_prosodic_features(self, audio: np.ndarray) -> np.ndarray:
        """Extract prosodic features (pitch, energy, etc.) from audio."""
        # Pitch features
        pitch, _ = librosa.piptrack(y=audio, sr=self.sampling_rate)
        pitch_vals = pitch[pitch > 0]
        pitch_mean = pitch_vals.mean() if len(pitch_vals) > 0 else 0
        pitch_std = pitch_vals.std() if len(pitch_vals) > 0 else 0
        
        # Energy
        energy = np.sum(audio ** 2) / len(audio)
        
        # Zero crossing rate
        zcr = librosa.feature.zero_crossing_rate(audio).mean()
        
        # Jitter and shimmer
        jitter = np.mean(np.abs(np.diff(audio)))
        shimmer = np.std(np.diff(audio))
        
        prosodic_features = np.array([pitch_mean, pitch_std, energy, zcr, jitter, shimmer])
        return prosodic_features
    
    def extract_features_from_audio(self, audio: np.ndarray) -> np.ndarray:
        """Extract all features from a single audio array."""
        # Apply augmentation if enabled
        if self.augment:
            audio = self.augment_audio(audio, self.sampling_rate)
        
        # Extract different feature types
        wav2vec2_features = self.extract_wav2vec2_features(audio)
        mfcc_features = self.extract_mfcc_features(audio)
        prosodic_features = self.extract_prosodic_features(audio)
        
        # Concatenate all features
        all_features = np.concatenate([wav2vec2_features, mfcc_features, prosodic_features])
        return all_features
    
    def __call__(
        self,
        raw_audio: Union[np.ndarray, List[float], List[np.ndarray], List[List[float]]],
        sampling_rate: Optional[int] = None,
        return_tensors: Optional[str] = None,
        **kwargs
    ) -> BatchFeature:
        """
        Main feature extraction method.
        
        Args:
            raw_audio: Raw audio data as numpy array(s) or list(s)
            sampling_rate: Sampling rate of the audio
            return_tensors: Type of tensors to return ('pt', 'np', etc.)
            
        Returns:
            BatchFeature containing extracted features
        """
        if sampling_rate is not None and sampling_rate != self.sampling_rate:
            logger.warning(
                f"Provided sampling_rate {sampling_rate} differs from expected {self.sampling_rate}. "
                "Audio will be resampled."
            )
        
        # Handle different input formats
        if isinstance(raw_audio, np.ndarray):
            if raw_audio.ndim == 1:
                # Single audio array
                raw_audio = [raw_audio]
            elif raw_audio.ndim == 2:
                # Batch of audio arrays
                raw_audio = [audio for audio in raw_audio]
        elif isinstance(raw_audio, list):
            if len(raw_audio) > 0 and isinstance(raw_audio[0], (int, float)):
                # Single audio as list
                raw_audio = [np.array(raw_audio)]
            else:
                # Batch of audio arrays
                raw_audio = [np.array(audio) for audio in raw_audio]
        else:
            raise ValueError(f"Unsupported raw_audio type: {type(raw_audio)}")
        
        # Resample if necessary
        target_sr = sampling_rate or self.sampling_rate
        if target_sr != self.sampling_rate:
            raw_audio = [
                librosa.resample(audio, orig_sr=target_sr, target_sr=self.sampling_rate)
                for audio in raw_audio
            ]
        
        # Extract features for each audio
        all_features = []
        for audio in raw_audio:
            features = self.extract_features_from_audio(audio)
            all_features.append(features)
        
        # Convert to appropriate format
        features_array = np.array(all_features)
        
        # Create batch features
        batch_features = BatchFeature({"input_features": features_array})
        
        # Convert to requested tensor type
        if return_tensors == "pt":
            batch_features = BatchFeature({
                key: torch.tensor(value, dtype=torch.float32) 
                for key, value in batch_features.items()
            })
        elif return_tensors == "np":
            batch_features = BatchFeature({
                key: np.array(value, dtype=np.float32) 
                for key, value in batch_features.items()
            })
        
        return batch_features
    
    def from_file(
        self,
        audio_file_path: str,
        sampling_rate: Optional[int] = None,
        return_tensors: Optional[str] = None,
        **kwargs
    ) -> BatchFeature:
        """Load audio from file and extract features."""
        # Load audio file
        audio, sr = librosa.load(audio_file_path, sr=sampling_rate or self.sampling_rate)
        
        # Extract features
        return self(audio, sampling_rate=sr, return_tensors=return_tensors, **kwargs)


# Register the feature extractor with Transformers
try:
    from transformers import AutoFeatureExtractor
    AutoFeatureExtractor.register(EmotionAVFeatureExtractor, EmotionAVFeatureExtractor)
except ImportError:
    # If transformers is not available, skip registration
    pass 