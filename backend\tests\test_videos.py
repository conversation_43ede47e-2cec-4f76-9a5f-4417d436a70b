import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

from app.models.video import VideoStatus


class TestVideoEndpoints:
    """Test video-related endpoints"""
    
    def test_list_videos_success(self, client: TestClient, auth_headers, test_video):
        """Test listing user's videos"""
        response = client.get("/api/videos/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "videos" in data
        assert "total" in data
        assert len(data["videos"]) >= 1
    
    def test_list_videos_with_filter(self, client: TestClient, auth_headers, test_video):
        """Test listing videos with status filter"""
        response = client.get(
            "/api/videos/?status_filter=completed", 
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "videos" in data
    
    def test_list_videos_unauthorized(self, client: TestClient):
        """Test listing videos without authentication"""
        response = client.get("/api/videos/")
        
        assert response.status_code == 401
    
    def test_get_video_success(self, client: TestClient, auth_headers, test_video):
        """Test getting specific video"""
        response = client.get(f"/api/videos/{test_video.id}", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_video.id
        assert data["title"] == test_video.title
    
    def test_get_video_not_found(self, client: TestClient, auth_headers):
        """Test getting non-existent video"""
        response = client.get("/api/videos/99999", headers=auth_headers)
        
        assert response.status_code == 404
    
    def test_get_video_unauthorized(self, client: TestClient, test_video):
        """Test getting video without authentication"""
        response = client.get(f"/api/videos/{test_video.id}")
        
        assert response.status_code == 401
    
    @patch('app.services.video_generation.generate_video_task')
    def test_generate_video_success(self, mock_generate_task, client: TestClient, auth_headers):
        """Test successful video generation request"""
        video_data = {
            "title": "Test Generated Video",
            "prompt_text": "A beautiful sunset over the ocean",
            "model": "stable_video_diffusion",
            "resolution": "1024x576",
            "duration_seconds": 4.0,
            "guidance_scale": 7.5,
            "num_inference_steps": 25
        }
        
        response = client.post("/api/videos/generate", json=video_data, headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == video_data["title"]
        assert data["prompt_text"] == video_data["prompt_text"]
        assert data["status"] == VideoStatus.PENDING
    
    def test_generate_video_invalid_data(self, client: TestClient, auth_headers):
        """Test video generation with invalid data"""
        invalid_data = {
            "title": "",  # Empty title
            "prompt_text": "Short",  # Too short
        }
        
        response = client.post("/api/videos/generate", json=invalid_data, headers=auth_headers)
        
        assert response.status_code == 422  # Validation error
    
    def test_generate_video_unauthorized(self, client: TestClient):
        """Test video generation without authentication"""
        video_data = {
            "title": "Test Video",
            "prompt_text": "A test prompt for video generation",
            "model": "stable_video_diffusion"
        }
        
        response = client.post("/api/videos/generate", json=video_data)
        
        assert response.status_code == 401
    
    def test_delete_video_success(self, client: TestClient, auth_headers, test_video):
        """Test successful video deletion"""
        response = client.delete(f"/api/videos/{test_video.id}", headers=auth_headers)
        
        assert response.status_code == 200
        assert "successfully deleted" in response.json()["message"]
    
    def test_delete_video_not_found(self, client: TestClient, auth_headers):
        """Test deleting non-existent video"""
        response = client.delete("/api/videos/99999", headers=auth_headers)
        
        assert response.status_code == 404
    
    def test_delete_video_unauthorized(self, client: TestClient, test_video):
        """Test deleting video without authentication"""
        response = client.delete(f"/api/videos/{test_video.id}")
        
        assert response.status_code == 401
    
    def test_download_video_success(self, client: TestClient, auth_headers, test_video):
        """Test video download request"""
        # Update video to completed status with output URL
        test_video.status = VideoStatus.COMPLETED
        test_video.output_video_url = "http://example.com/video.mp4"
        
        response = client.post(f"/api/videos/{test_video.id}/download", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "download_url" in data
        assert "filename" in data
    
    def test_download_video_not_ready(self, client: TestClient, auth_headers, test_video):
        """Test downloading video that's not ready"""
        # Ensure video is not completed
        test_video.status = VideoStatus.PROCESSING
        
        response = client.post(f"/api/videos/{test_video.id}/download", headers=auth_headers)
        
        assert response.status_code == 400
        assert "not ready for download" in response.json()["detail"]


class TestVideoGeneration:
    """Test video generation logic"""
    
    @patch('app.services.video_generation.VideoGenerationService')
    def test_video_generation_task(self, mock_service_class):
        """Test video generation background task"""
        from app.services.video_generation import generate_video_task
        
        # Mock the service
        mock_service = MagicMock()
        mock_service_class.return_value = mock_service
        mock_service.generate_with_stable_video_diffusion.return_value = "/path/to/video.mp4"
        mock_service.create_thumbnail.return_value = "/path/to/thumbnail.jpg"
        
        # This would normally be called by Celery
        # generate_video_task(1)  # Commented out as it requires database setup
        
        # Verify service was instantiated
        # mock_service_class.assert_called_once()
    
    def test_video_status_transitions(self, db_session, test_user):
        """Test video status transitions"""
        from app.models.video import Video, VideoStatus
        
        video = Video(
            user_id=test_user.id,
            title="Status Test Video",
            prompt_text="Test prompt",
            status=VideoStatus.PENDING
        )
        db_session.add(video)
        db_session.commit()
        
        # Test status properties
        assert video.is_processing == True
        assert video.is_completed == False
        assert video.has_failed == False
        
        # Update to completed
        video.status = VideoStatus.COMPLETED
        db_session.commit()
        
        assert video.is_processing == False
        assert video.is_completed == True
        assert video.has_failed == False
    
    def test_video_resolution_properties(self, test_video):
        """Test video resolution property methods"""
        test_video.resolution = "1920x1080"
        
        assert test_video.resolution_width == 1920
        assert test_video.resolution_height == 1080
    
    def test_estimated_processing_time(self, test_video):
        """Test processing time estimation"""
        test_video.duration_seconds = 10.0
        test_video.resolution = "1920x1080"
        test_video.num_inference_steps = 50
        
        estimated_time = test_video.estimated_processing_time
        
        assert isinstance(estimated_time, int)
        assert estimated_time > 0
