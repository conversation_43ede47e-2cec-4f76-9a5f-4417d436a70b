from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    
    # Google OAuth fields
    google_id = Column(String(255), unique=True, index=True, nullable=True)
    google_verified = Column(Boolean, default=False)
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_premium = Column(Boolean, default=False)
    
    # Usage tracking
    videos_generated = Column(Integer, default=0)
    storage_used_mb = Column(Integer, default=0)
    
    # Subscription info
    subscription_type = Column(String(50), default="free")  # free, premium, enterprise
    subscription_expires_at = Column(DateTime, nullable=True)
    
    # API access
    api_key = Column(String(255), unique=True, nullable=True)
    api_calls_count = Column(Integer, default=0)
    api_calls_limit = Column(Integer, default=100)  # per month
    
    # Preferences
    preferences = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    videos = relationship("Video", back_populates="user", cascade="all, delete-orphan")
    prompts = relationship("Prompt", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', full_name='{self.full_name}')>"
    
    @property
    def is_premium_active(self) -> bool:
        """Check if user has active premium subscription"""
        if not self.is_premium:
            return False
        if self.subscription_expires_at is None:
            return True
        from datetime import datetime
        return self.subscription_expires_at > datetime.utcnow()
    
    @property
    def can_generate_video(self) -> bool:
        """Check if user can generate more videos based on their plan"""
        if self.is_premium_active:
            return True
        # Free users have limits
        return self.videos_generated < 5  # 5 videos per month for free users
    
    @property
    def remaining_api_calls(self) -> int:
        """Get remaining API calls for the month"""
        return max(0, self.api_calls_limit - self.api_calls_count)
