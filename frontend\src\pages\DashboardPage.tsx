import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/services/api';
import LoadingSpinner from '@/components/LoadingSpinner';
import { 
  Video, 
  Sparkles, 
  TrendingUp, 
  Clock, 
  Download,
  Eye,
  Plus,
  ArrowRight,
  Crown,
  Zap
} from 'lucide-react';
import { VideoStatus } from '@/types';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  // Fetch user stats
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['user-stats'],
    queryFn: () => apiService.getUserStats(),
  });

  // Fetch recent videos
  const { data: recentVideos, isLoading: videosLoading } = useQuery({
    queryKey: ['recent-videos'],
    queryFn: () => apiService.getVideos(1, 6),
  });

  if (statsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case VideoStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case VideoStatus.PROCESSING:
        return 'bg-blue-100 text-blue-800';
      case VideoStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case VideoStatus.FAILED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">
              Welcome back, {user?.full_name || user?.email?.split('@')[0]}!
            </h1>
            <p className="text-primary-100">
              Ready to create some amazing videos today?
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {user?.is_premium && (
              <div className="flex items-center space-x-1 bg-yellow-500 text-yellow-900 px-3 py-1 rounded-full text-sm font-medium">
                <Crown className="h-4 w-4" />
                <span>Premium</span>
              </div>
            )}
            <Link
              to="/generate"
              className="bg-white text-primary-600 hover:bg-gray-50 px-4 py-2 rounded-md font-medium transition-colors flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Create Video</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Video className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Videos</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.total_videos || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.completed_videos || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Processing</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.processing_videos || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Zap className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">API Calls Left</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats?.api_calls_remaining || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link
          to="/generate"
          className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow group"
        >
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <Sparkles className="h-8 w-8 text-primary-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900 group-hover:text-primary-600">
                Generate New Video
              </h3>
              <p className="text-sm text-gray-500">
                Create videos from text prompts or images
              </p>
            </div>
            <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-primary-600" />
          </div>
        </Link>

        <Link
          to="/prompts"
          className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow group"
        >
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 font-semibold">P</span>
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900 group-hover:text-green-600">
                Browse Prompts
              </h3>
              <p className="text-sm text-gray-500">
                Explore and save creative prompts
              </p>
            </div>
            <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-green-600" />
          </div>
        </Link>

        <Link
          to="/videos"
          className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow group"
        >
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <Video className="h-8 w-8 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600">
                My Videos
              </h3>
              <p className="text-sm text-gray-500">
                View and manage your video library
              </p>
            </div>
            <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-600" />
          </div>
        </Link>
      </div>

      {/* Recent Videos */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium text-gray-900">Recent Videos</h2>
            <Link
              to="/videos"
              className="text-sm text-primary-600 hover:text-primary-700 font-medium"
            >
              View all
            </Link>
          </div>
        </div>
        <div className="p-6">
          {videosLoading ? (
            <div className="flex items-center justify-center h-32">
              <LoadingSpinner text="Loading videos..." />
            </div>
          ) : recentVideos?.items.length === 0 ? (
            <div className="text-center py-8">
              <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No videos yet</h3>
              <p className="text-gray-500 mb-4">
                Create your first video to get started
              </p>
              <Link
                to="/generate"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Video
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentVideos?.items.map((video) => (
                <Link
                  key={video.id}
                  to={`/videos/${video.id}`}
                  className="group block"
                >
                  <div className="bg-gray-100 rounded-lg aspect-video mb-3 overflow-hidden">
                    {video.thumbnail_url ? (
                      <img
                        src={video.thumbnail_url}
                        alt={video.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Video className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-900 group-hover:text-primary-600 truncate">
                      {video.title}
                    </h3>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(video.status)}`}>
                        {video.status}
                      </span>
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1">
                          <Eye className="h-3 w-3" />
                          <span>{video.view_count}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Download className="h-3 w-3" />
                          <span>{video.download_count}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Upgrade Banner (for free users) */}
      {!user?.is_premium && (
        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2">
                Upgrade to Premium
              </h3>
              <p className="text-yellow-100">
                Unlock unlimited video generation, priority processing, and advanced features.
              </p>
            </div>
            <Link
              to="/profile"
              className="bg-white text-orange-600 hover:bg-gray-50 px-4 py-2 rounded-md font-medium transition-colors flex items-center space-x-2"
            >
              <Crown className="h-4 w-4" />
              <span>Upgrade Now</span>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardPage;
