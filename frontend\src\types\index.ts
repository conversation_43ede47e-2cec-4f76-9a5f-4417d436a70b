// User types
export interface User {
  id: number;
  email: string;
  full_name?: string;
  avatar_url?: string;
  is_premium: boolean;
  subscription_type: string;
  videos_generated: number;
  storage_used_mb: number;
  api_calls_count: number;
  api_calls_limit: number;
  created_at: string;
  last_login_at?: string;
}

export interface UserStats {
  total_videos: number;
  completed_videos: number;
  processing_videos: number;
  failed_videos: number;
  total_storage_mb: number;
  api_calls_used: number;
  api_calls_remaining: number;
}

// Video types
export interface Video {
  id: number;
  title: string;
  description?: string;
  prompt_text: string;
  negative_prompt?: string;
  input_image_url?: string;
  output_video_url?: string;
  thumbnail_url?: string;
  status: VideoStatus;
  progress_percentage: number;
  model_used: string;
  resolution: string;
  duration_seconds: number;
  file_size_mb?: number;
  processing_time_seconds?: number;
  view_count: number;
  download_count: number;
  created_at: string;
  error_message?: string;
}

export enum VideoStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum VideoModel {
  STABLE_VIDEO_DIFFUSION = 'stable_video_diffusion',
  RUNWAYML = 'runwayml',
  OPENAI_SORA = 'openai_sora',
  CUSTOM = 'custom'
}

// Prompt types
export interface Prompt {
  id: number;
  title: string;
  prompt_text: string;
  negative_prompt?: string;
  category?: string;
  style?: string;
  mood?: string;
  tags: string[];
  use_count: number;
  success_rate: number;
  average_rating: number;
  is_public: boolean;
  created_at: string;
}

// API request/response types
export interface VideoGenerationRequest {
  title: string;
  prompt_text: string;
  negative_prompt?: string;
  input_image_url?: string;
  model: VideoModel;
  resolution: string;
  duration_seconds: number;
  guidance_scale: number;
  num_inference_steps: number;
  seed?: number;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  user: User;
}

export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Upload types
export interface UploadLimits {
  max_file_size_mb: number;
  storage_limit_mb: number;
  storage_used_mb: number;
  storage_remaining_mb: number;
  allowed_image_extensions: string[];
  allowed_video_extensions: string[];
}

export interface UploadResponse {
  message: string;
  filename: string;
  file_path: string;
  file_url: string;
  file_size_mb: number;
}

// Form types
export interface VideoGenerationForm {
  title: string;
  prompt: string;
  negativePrompt?: string;
  model: VideoModel;
  resolution: string;
  duration: number;
  guidanceScale: number;
  steps: number;
  seed?: number;
  inputImage?: File;
}

export interface PromptForm {
  title: string;
  prompt_text: string;
  negative_prompt?: string;
  category?: string;
  style?: string;
  mood?: string;
  tags: string[];
  is_public: boolean;
}

// UI types
export interface ToastOptions {
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message?: string;
  duration?: number;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

// Auth types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface GoogleAuthResponse {
  credential: string;
  select_by: string;
}

// Settings types
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  defaultModel: VideoModel;
  defaultResolution: string;
  defaultDuration: number;
  autoSavePrompts: boolean;
  emailNotifications: boolean;
}

// Error types
export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: Record<string, any>;
}
