from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr
from typing import Optional
import logging

from app.core.database import get_db
from app.core.security import (
    create_access_token, 
    create_refresh_token, 
    verify_token, 
    verify_google_token,
    get_current_user_token
)
from app.models.user import User
from app.core.config import settings

logger = logging.getLogger(__name__)
router = APIRouter()
security = HTTPBearer()


# Pydantic models for request/response
class GoogleAuthRequest(BaseModel):
    token: str


class LoginResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user: dict


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class UserRegistration(BaseModel):
    email: EmailStr
    full_name: str
    google_id: Optional[str] = None


@router.post("/google", response_model=LoginResponse)
async def google_auth(
    auth_request: GoogleAuthRequest,
    db: Session = Depends(get_db)
):
    """Authenticate user with Google OAuth token"""
    try:
        # Verify Google token
        google_user_info = await verify_google_token(auth_request.token)
        if not google_user_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Google token"
            )
        
        email = google_user_info.get("email")
        google_id = google_user_info.get("sub")
        full_name = google_user_info.get("name", "")
        avatar_url = google_user_info.get("picture", "")
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email not provided by Google"
            )
        
        # Check if user exists
        user = db.query(User).filter(User.email == email).first()
        
        if not user:
            # Create new user
            user = User(
                email=email,
                full_name=full_name,
                avatar_url=avatar_url,
                google_id=google_id,
                google_verified=True,
                is_verified=True,
                is_active=True
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            logger.info(f"New user created: {email}")
        else:
            # Update existing user with Google info
            user.google_id = google_id
            user.google_verified = True
            user.avatar_url = avatar_url
            if not user.full_name:
                user.full_name = full_name
            user.is_verified = True
            db.commit()
            logger.info(f"Existing user updated: {email}")
        
        # Create tokens
        token_data = {"sub": str(user.id), "email": user.email}
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)
        
        # Update last login
        from sqlalchemy.sql import func
        user.last_login_at = func.now()
        db.commit()
        
        return LoginResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            user={
                "id": user.id,
                "email": user.email,
                "full_name": user.full_name,
                "avatar_url": user.avatar_url,
                "is_premium": user.is_premium,
                "subscription_type": user.subscription_type,
                "videos_generated": user.videos_generated
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Google auth error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


@router.post("/refresh", response_model=dict)
async def refresh_access_token(
    refresh_request: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token"""
    try:
        # Verify refresh token
        payload = verify_token(refresh_request.refresh_token, token_type="refresh")
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid refresh token"
            )
        
        user_id = payload.get("sub")
        email = payload.get("email")
        
        # Verify user still exists and is active
        user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )
        
        # Create new access token
        token_data = {"sub": str(user.id), "email": user.email}
        access_token = create_access_token(token_data)
        
        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout(current_user: dict = Depends(get_current_user_token)):
    """Logout user (client should discard tokens)"""
    logger.info(f"User logged out: {current_user['email']}")
    return {"message": "Successfully logged out"}


@router.get("/me")
async def get_current_user(
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
):
    """Get current user information"""
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return {
        "id": user.id,
        "email": user.email,
        "full_name": user.full_name,
        "avatar_url": user.avatar_url,
        "is_premium": user.is_premium,
        "subscription_type": user.subscription_type,
        "videos_generated": user.videos_generated,
        "storage_used_mb": user.storage_used_mb,
        "api_calls_count": user.api_calls_count,
        "api_calls_limit": user.api_calls_limit,
        "created_at": user.created_at,
        "last_login_at": user.last_login_at
    }


@router.get("/config")
async def get_auth_config():
    """Get authentication configuration for frontend"""
    return {
        "google_client_id": settings.GOOGLE_CLIENT_ID,
        "redirect_uri": settings.GOOGLE_REDIRECT_URI
    }
