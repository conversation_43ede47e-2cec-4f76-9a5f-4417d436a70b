import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Video, 
  Sparkles, 
  Upload, 
  Download, 
  Users, 
  Star,
  ArrowRight,
  Play,
  Zap,
  Shield,
  Globe
} from 'lucide-react';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: Sparkles,
      title: 'AI-Powered Generation',
      description: 'Create stunning videos from text prompts using state-of-the-art AI models like Stable Video Diffusion and RunwayML.'
    },
    {
      icon: Upload,
      title: 'Image to Video',
      description: 'Transform your images into dynamic videos with smooth motion and cinematic effects.'
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Generate high-quality videos in minutes, not hours. Our optimized pipeline ensures quick turnaround times.'
    },
    {
      icon: Shield,
      title: 'Secure & Private',
      description: 'Your content is protected with enterprise-grade security. We never share or use your data for training.'
    },
    {
      icon: Globe,
      title: 'Cloud Storage',
      description: 'Access your videos from anywhere with secure cloud storage and easy sharing options.'
    },
    {
      icon: Download,
      title: 'Multiple Formats',
      description: 'Download your videos in various formats and resolutions optimized for different platforms.'
    }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Content Creator',
      avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=3b82f6&color=fff',
      content: 'This platform has revolutionized my content creation workflow. The AI-generated videos are incredibly realistic and save me hours of work.'
    },
    {
      name: 'Marcus Rodriguez',
      role: 'Marketing Director',
      avatar: 'https://ui-avatars.com/api/?name=Marcus+Rodriguez&background=10b981&color=fff',
      content: 'The quality of videos generated is outstanding. Our social media engagement has increased by 300% since we started using VideoGen Studio.'
    },
    {
      name: 'Emily Johnson',
      role: 'Filmmaker',
      avatar: 'https://ui-avatars.com/api/?name=Emily+Johnson&background=f59e0b&color=fff',
      content: 'As a filmmaker, I was skeptical about AI video generation. But this tool has become an essential part of my pre-production process.'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-2">
              <Video className="h-8 w-8 text-primary-600" />
              <span className="text-2xl font-bold text-gray-900">VideoGen Studio</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
              >
                Sign In
              </Link>
              <Link
                to="/login"
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Create Stunning Videos with
              <span className="text-primary-600 block">AI Magic</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Transform your ideas into captivating videos using cutting-edge AI technology. 
              Generate professional-quality videos from text prompts or images in minutes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/login"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors"
              >
                Start Creating
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <button className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Powerful Features for Every Creator
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Everything you need to create professional videos with AI assistance
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0">
                    <feature.icon className="h-8 w-8 text-primary-600" />
                  </div>
                  <h3 className="ml-3 text-lg font-semibold text-gray-900">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-white mb-2">10K+</div>
              <div className="text-primary-100">Videos Generated</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">5K+</div>
              <div className="text-primary-100">Happy Users</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">99.9%</div>
              <div className="text-primary-100">Uptime</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-white mb-2">24/7</div>
              <div className="text-primary-100">Support</div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Creators Worldwide
            </h2>
            <p className="text-xl text-gray-600">
              See what our users are saying about VideoGen Studio
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4">"{testimonial.content}"</p>
                <div className="flex items-center">
                  <img
                    className="h-10 w-10 rounded-full"
                    src={testimonial.avatar}
                    alt={testimonial.name}
                  />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">{testimonial.name}</p>
                    <p className="text-sm text-gray-500">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Ready to Create Amazing Videos?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of creators who are already using AI to bring their ideas to life.
          </p>
          <Link
            to="/login"
            className="inline-flex items-center px-8 py-3 border border-transparent text-lg font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors"
          >
            Start Your Free Trial
            <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Video className="h-6 w-6 text-primary-400" />
              <span className="text-lg font-semibold">VideoGen Studio</span>
            </div>
            <p className="text-gray-400">
              © 2024 VideoGen Studio. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
