# Video Generation Application

A modern full-stack video generation application that allows users to create AI-generated videos from text prompts and images.

## 🚀 Features

- **AI Video Generation**: Generate videos from text prompts using state-of-the-art AI models
- **Image-to-Video**: Upload images and convert them to videos with AI enhancement
- **Google Authentication**: Secure login with Google OAuth
- **User Dashboard**: Manage your generated videos and prompts
- **Real-time Progress**: Track video generation progress in real-time
- **Download & Share**: Download generated videos or share them directly

## 🏗️ Architecture

### Backend (Python FastAPI)
- **FastAPI**: Modern, fast web framework for building APIs
- **PostgreSQL**: Primary database for user data and video metadata
- **Redis**: Caching and session management
- **Celery**: Background task processing for video generation
- **SQLAlchemy**: ORM for database operations
- **Pydantic**: Data validation and serialization

### Frontend (React.js)
- **React 18**: Modern React with hooks and concurrent features
- **TypeScript**: Type-safe JavaScript development
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **React Query**: Data fetching and state management
- **React Router**: Client-side routing

### AI Video Generation
- **Stable Video Diffusion**: Open-source video generation model
- **RunwayML API**: Commercial video generation service
- **OpenAI DALL-E**: Image generation for video frames
- **Hugging Face Transformers**: Additional AI model support

### Infrastructure
- **Docker**: Containerization for easy deployment
- **AWS S3/CloudFlare R2**: File storage for videos and images
- **Redis**: Background job queue and caching
- **PostgreSQL**: Primary database

## 📁 Project Structure

```
video-gen-app/
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── tests/              # Backend tests
│   ├── requirements.txt    # Python dependencies
│   └── Dockerfile         # Backend container
├── frontend/               # React.js frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   ├── package.json        # Node.js dependencies
│   └── Dockerfile         # Frontend container
├── docker-compose.yml      # Development environment
├── .env.example           # Environment variables template
└── docs/                  # Documentation
```

## 🛠️ Development Setup

### Prerequisites
- Python 3.11+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### Environment Variables
Copy `.env.example` to `.env` and fill in the required values:
- Google OAuth credentials
- Database connection strings
- AI model API keys
- Cloud storage credentials

### Quick Start
```bash
# Clone and setup
git clone <repository>
cd video-gen-app

# Start development environment
docker-compose up -d

# Install backend dependencies
cd backend
pip install -r requirements.txt

# Install frontend dependencies
cd ../frontend
npm install

# Start development servers
npm run dev  # Frontend (http://localhost:3000)
# Backend will be at http://localhost:8000
```

## 🔑 API Keys Required

You'll need to obtain API keys for:
- **Google OAuth**: Client ID and Secret
- **OpenAI**: For DALL-E image generation
- **RunwayML**: For commercial video generation
- **Hugging Face**: For open-source models
- **AWS S3 or CloudFlare R2**: For file storage

## 📚 API Documentation

Once the backend is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test
```

## 🚀 Deployment

The application is containerized and ready for deployment on:
- AWS ECS/EKS
- Google Cloud Run
- Azure Container Instances
- DigitalOcean App Platform

## 📄 License

MIT License - see LICENSE file for details.
