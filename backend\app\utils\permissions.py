from functools import wraps
from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session
from typing import Callable, Any

from app.core.database import get_db
from app.core.security import get_current_user_token
from app.models.user import User
from app.models.video import Video
from app.models.prompt import Prompt


def require_premium(func: Callable) -> Callable:
    """Decorator to require premium subscription"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Extract user from kwargs or dependencies
        current_user = kwargs.get('current_user')
        if not current_user:
            # Try to get from dependencies
            for arg in args:
                if isinstance(arg, dict) and 'user_id' in arg:
                    db = kwargs.get('db')
                    if db:
                        user = db.query(User).filter(User.id == arg['user_id']).first()
                        if user and not user.is_premium_active:
                            raise HTTPException(
                                status_code=status.HTTP_403_FORBIDDEN,
                                detail="This feature requires a premium subscription"
                            )
                    break
        elif hasattr(current_user, 'is_premium_active') and not current_user.is_premium_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="This feature requires a premium subscription"
            )
        
        return await func(*args, **kwargs)
    return wrapper


def require_quota(func: Callable) -> Callable:
    """Decorator to check API quota"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        current_user = kwargs.get('current_user')
        db = kwargs.get('db')
        
        if current_user and db:
            user = db.query(User).filter(User.id == current_user['user_id']).first()
            if user and user.remaining_api_calls <= 0:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="API quota exceeded. Please upgrade your plan or wait for quota reset."
                )
            
            # Increment API call count
            if user:
                user.api_calls_count += 1
                db.commit()
        
        return await func(*args, **kwargs)
    return wrapper


class PermissionChecker:
    """Class to handle various permission checks"""
    
    @staticmethod
    def can_access_video(user: User, video: Video) -> bool:
        """Check if user can access a video"""
        return video.user_id == user.id or video.is_public
    
    @staticmethod
    def can_edit_video(user: User, video: Video) -> bool:
        """Check if user can edit a video"""
        return video.user_id == user.id
    
    @staticmethod
    def can_delete_video(user: User, video: Video) -> bool:
        """Check if user can delete a video"""
        return video.user_id == user.id
    
    @staticmethod
    def can_access_prompt(user: User, prompt: Prompt) -> bool:
        """Check if user can access a prompt"""
        return prompt.user_id == user.id or prompt.is_public
    
    @staticmethod
    def can_edit_prompt(user: User, prompt: Prompt) -> bool:
        """Check if user can edit a prompt"""
        return prompt.user_id == user.id
    
    @staticmethod
    def can_delete_prompt(user: User, prompt: Prompt) -> bool:
        """Check if user can delete a prompt"""
        return prompt.user_id == user.id
    
    @staticmethod
    def can_generate_video(user: User) -> bool:
        """Check if user can generate videos"""
        if user.is_premium_active:
            return True
        # Free users have limits
        return user.videos_generated < 5  # 5 videos per month for free users
    
    @staticmethod
    def can_upload_file(user: User, file_size_mb: float) -> bool:
        """Check if user can upload a file"""
        storage_limit_mb = 10240 if user.is_premium else 1024  # 10GB vs 1GB
        return (user.storage_used_mb + file_size_mb) <= storage_limit_mb
    
    @staticmethod
    def can_use_premium_model(user: User, model: str) -> bool:
        """Check if user can use premium AI models"""
        premium_models = ['runwayml', 'openai_sora']
        if model.lower() in premium_models:
            return user.is_premium_active
        return True


def check_video_access(
    video_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> Video:
    """Dependency to check video access permissions"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found"
        )
    
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if not PermissionChecker.can_access_video(user, video):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return video


def check_video_ownership(
    video_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> Video:
    """Dependency to check video ownership"""
    video = db.query(Video).filter(
        Video.id == video_id,
        Video.user_id == current_user["user_id"]
    ).first()
    
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Video not found or access denied"
        )
    
    return video


def check_prompt_access(
    prompt_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> Prompt:
    """Dependency to check prompt access permissions"""
    prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Prompt not found"
        )
    
    user = db.query(User).filter(User.id == current_user["user_id"]).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    if not PermissionChecker.can_access_prompt(user, prompt):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return prompt


def check_prompt_ownership(
    prompt_id: int,
    current_user: dict = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> Prompt:
    """Dependency to check prompt ownership"""
    prompt = db.query(Prompt).filter(
        Prompt.id == prompt_id,
        Prompt.user_id == current_user["user_id"]
    ).first()
    
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Prompt not found or access denied"
        )
    
    return prompt
