from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from .config import settings
import logging

logger = logging.getLogger(__name__)

# Sync database setup
engine = create_engine(
    settings.DATABASE_URL,
    echo=settings.DATABASE_ECHO,
    pool_pre_ping=True,
    pool_recycle=300,
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Async database setup (disabled for SQLite)
# async_engine = create_async_engine(
#     settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
#     echo=settings.DATABASE_ECHO,
#     pool_pre_ping=True,
#     pool_recycle=300,
# )

# AsyncSessionLocal = async_sessionmaker(
#     async_engine,
#     class_=AsyncSession,
#     expire_on_commit=False
# )

# Base class for models
Base = declarative_base()


# Dependency to get database session
def get_db():
    """Dependency to get sync database session"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()


# async def get_async_db():
#     """Dependency to get async database session"""
#     async with AsyncSessionLocal() as session:
#         try:
#             yield session
#         except Exception as e:
#             logger.error(f"Async database session error: {e}")
#             await session.rollback()
#             raise
#         finally:
#             await session.close()


async def init_db():
    """Initialize database tables"""
    try:
        async with async_engine.begin() as conn:
            # Import all models here to ensure they are registered
            from app.models import user, video, prompt
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise


async def close_db():
    """Close database connections"""
    await async_engine.dispose()
    engine.dispose()
    logger.info("Database connections closed")
