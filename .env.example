# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/videogen_db
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
SECRET_KEY=your-super-secret-jwt-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:3000/auth/callback

# AI Model API Keys
OPENAI_API_KEY=your-openai-api-key
RUNWAYML_API_KEY=your-runwayml-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key
STABILITY_API_KEY=your-stability-ai-api-key

# Cloud Storage Configuration (Choose one)
# AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=your-s3-bucket-name
AWS_REGION=us-east-1

# CloudFlare R2 (Alternative to S3)
CLOUDFLARE_R2_ACCESS_KEY_ID=your-r2-access-key
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-r2-secret-key
CLOUDFLARE_R2_BUCKET_NAME=your-r2-bucket-name
CLOUDFLARE_R2_ENDPOINT=your-r2-endpoint

# Application Configuration
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000
ENVIRONMENT=development

# File Upload Configuration
MAX_FILE_SIZE_MB=100
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp
ALLOWED_VIDEO_EXTENSIONS=mp4,avi,mov,webm

# Video Generation Configuration
MAX_VIDEO_DURATION_SECONDS=30
DEFAULT_VIDEO_RESOLUTION=1024x576
MAX_CONCURRENT_GENERATIONS=5

# Email Configuration (Optional - for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Monitoring & Logging
LOG_LEVEL=INFO
SENTRY_DSN=your-sentry-dsn-for-error-tracking
