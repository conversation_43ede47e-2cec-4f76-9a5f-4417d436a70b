#!/usr/bin/env python3
"""
Script to migrate models from user cache to local project directory
"""
import os
import shutil
from pathlib import Path

def migrate_models():
    """Migrate models from user cache to local directory"""
    
    # Define paths
    project_root = Path(__file__).parent
    local_models_dir = project_root / "models"
    
    # User cache directories
    user_home = Path.home()
    cache_dirs = {
        "huggingface": user_home / ".cache" / "huggingface",
        "transformers": user_home / ".cache" / "transformers", 
        "diffusers": user_home / ".cache" / "diffusers"
    }
    
    print(f"Migrating models to: {local_models_dir}")
    
    # Create local directories
    local_models_dir.mkdir(exist_ok=True)
    for cache_name in cache_dirs.keys():
        (local_models_dir / cache_name).mkdir(exist_ok=True)
    
    # Migrate each cache directory
    for cache_name, cache_path in cache_dirs.items():
        local_cache_path = local_models_dir / cache_name
        
        if cache_path.exists():
            print(f"\nMigrating {cache_name} cache...")
            print(f"From: {cache_path}")
            print(f"To: {local_cache_path}")
            
            # Copy files
            try:
                if cache_path.is_dir():
                    for item in cache_path.iterdir():
                        dest_item = local_cache_path / item.name
                        if item.is_dir():
                            if not dest_item.exists():
                                shutil.copytree(item, dest_item)
                                print(f"  Copied directory: {item.name}")
                            else:
                                print(f"  Skipped existing: {item.name}")
                        else:
                            if not dest_item.exists():
                                shutil.copy2(item, dest_item)
                                print(f"  Copied file: {item.name}")
                            else:
                                print(f"  Skipped existing: {item.name}")
                                
            except Exception as e:
                print(f"  Error migrating {cache_name}: {e}")
        else:
            print(f"\n{cache_name} cache not found at: {cache_path}")
    
    print(f"\nMigration complete!")
    print(f"Models are now stored locally in: {local_models_dir}")
    print(f"Total size: {get_directory_size(local_models_dir):.2f} GB")

def get_directory_size(path: Path) -> float:
    """Get directory size in GB"""
    total_size = 0
    try:
        for item in path.rglob('*'):
            if item.is_file():
                total_size += item.stat().st_size
    except Exception:
        pass
    return total_size / (1024**3)  # Convert to GB

if __name__ == "__main__":
    migrate_models()
